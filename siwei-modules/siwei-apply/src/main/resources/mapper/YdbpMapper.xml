<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siwei.apply.mapper.YdbpMapper">
    <!-- ResultMap 映射 -->
    <resultMap id="YdbpResultMap" type="com.siwei.apply.domain.Ydbp">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="tdyt" column="tdyt"/>
        <result property="ydArea" column="yd_area"/>
        <result property="zsArea" column="zs_area"/>
        <result property="hasZZ" column="has_zz"/>
        <result property="bpDate" column="bp_date"/>
        <result property="pfwh" column="pfwh"/>
        <result property="pfDate" column="pf_date"/>
        <result property="attachment" column="attachment" typeHandler="com.siwei.apply.handler.JsonbTypeHandler"/>
        <result property="hasOnchain" column="has_onchain"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>
    <select id="isExit" resultType="Boolean">
        SELECT COUNT(1) > 0
        FROM t_ydbp
        WHERE project_id = #{projectId}
    </select>
    <!-- 插入语句 -->
    <insert id="add" parameterType="com.siwei.apply.domain.Ydbp">
        INSERT INTO t_ydbp (id, project_id, tdyt, yd_area, zs_area, has_zz,
                            bp_date, pfwh, pf_date, has_onchain, creator_id, created_at, updated_at)
        VALUES (#{id}, #{projectId}, #{tdyt}, #{ydArea}, #{zsArea}, #{hasZZ},
                #{bpDate}, #{pfwh}, #{pfDate}, false, #{creatorId}, now(), now())
    </insert>

    <!-- 查询语句 -->
    <select id="get" resultMap="YdbpResultMap">
        SELECT *
        FROM t_ydbp
        WHERE project_id = #{projectId}
    </select>


    <!-- 更新语句 -->
    <update id="update" parameterType="com.siwei.apply.domain.vo.YdbpUpdateVo">
        UPDATE t_ydbp
        <set>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="tdyt != null">tdyt = #{tdyt},</if>
            <if test="ydArea != null">yd_area = #{ydArea},</if>
            <if test="zsArea != null">zs_area = #{zsArea},</if>
            <if test="hasZZ != null">has_zz = #{hasZZ},</if>
            <if test="bpDate != null">bp_date = #{bpDate},</if>
            <if test="pfwh != null">pfwh = #{pfwh},</if>
            <if test="pfDate != null">pf_date = #{pfDate},</if>
            <if test="hasOnchain != null">has_onchain = #{hasOnchain},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>
</mapper>