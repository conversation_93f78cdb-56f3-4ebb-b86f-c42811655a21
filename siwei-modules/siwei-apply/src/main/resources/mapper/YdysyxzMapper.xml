<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siwei.apply.mapper.YdysyxzMapper">
    <resultMap id="resMap" type="com.siwei.apply.domain.Ydysyxz">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="jsyj" column="jsyj"/>
        <result property="jsdd" column="jsdd"/>
        <result property="area" column="area"/>
        <result property="hasZz" column="has_zz"/>
        <result property="xmjsyj" column="xmjsyj"/>
        <result property="zzwh" column="zzwh"/>
        <result property="fzDate" column="fz_date"/>
        <result property="attachment" column="attachment" typeHandler="com.siwei.apply.handler.JsonbTypeHandler"/>
        <result property="hasOnchain" column="has_onchain"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="creatorId" column="creator_id"/>
    </resultMap>
    <select id="isExit" resultType="Boolean">
        SELECT COUNT(1) > 0
        FROM t_ydysyxz
        WHERE project_id = #{projectId}
    </select>

    <insert id="add" parameterType="com.siwei.apply.domain.Ydysyxz">
        INSERT INTO t_ydysyxz (id, project_id, jsyj, jsdd, area, has_zz,
                               xmjsyj, zzwh, fz_date, has_onchain, created_at, updated_at, creator_id)
        VALUES (#{id}, #{projectId}, #{jsyj}, #{jsdd}, #{area}, #{hasZz},
                #{xmjsyj}, #{zzwh}, #{fzDate},
                false, now(), now(), #{creatorId})
    </insert>
    <select id="get" resultMap="resMap">
        SELECT *
        FROM t_ydysyxz
        WHERE project_id = #{projectId}
    </select>
    <update id="update" parameterType="com.siwei.apply.domain.vo.YdysyxzUpdateVo">
        UPDATE t_ydysyxz
        <set>
            <if test="jsyj != null">jsyj = #{jsyj},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="jsdd != null">jsdd = #{jsdd},</if>
            <if test="area != null">area = #{area},</if>
            <if test="hasZz != null">has_zz = #{hasZz},</if>
            <if test="xmjsyj != null">xmjsyj = #{xmjsyj},</if>
            <if test="zzwh != null">zzwh = #{zzwh},</if>
            <if test="fzDate != null">fz_date = #{fzDate},</if>
            <if test="hasOnchain != null">has_onchain = #{hasOnchain},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>
</mapper>