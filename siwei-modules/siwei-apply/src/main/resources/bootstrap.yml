# Tomcat
server:
  port: 9201

# Spring
spring: 
  application:
    # 应用名称
    name: siwei-apply
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        enabled: true
        namespace: one_code
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
      config:
        # 配置中心地址
        enabled: true
        namespace: one_code
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
