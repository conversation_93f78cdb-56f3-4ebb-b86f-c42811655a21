package com.siwei.apply.service.impl;

import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.Ydysyxz;
import com.siwei.apply.domain.res.YdysyxzRes;
import com.siwei.apply.domain.vo.YdysyxzUpdateVo;
import com.siwei.apply.domain.vo.YdysyxzVo;
import com.siwei.apply.mapper.ProjectMapper;
import com.siwei.apply.mapper.YdysyxzMapper;
import com.siwei.apply.service.YdysyxzService;
import com.siwei.common.core.utils.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.siwei.apply.common.Common.UserId;

/**
 * 用地预审与选址 服务实现类
 */
@Service
public class YdysyxzImpl implements YdysyxzService {
    @Autowired
    private YdysyxzMapper ydysyxzMapper;
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public Boolean isExit(String projectId) {
        return ydysyxzMapper.isExit(projectId);
    }

    @Override
    public String add(YdysyxzVo ydysyxzVo) {
        Ydysyxz ydysyxz = new Ydysyxz();
        BeanUtils.copyProperties(ydysyxzVo, ydysyxz);
        ydysyxz.generateId();
        ydysyxz.setCreatorId(UserId);
        ydysyxzMapper.add(ydysyxz);
        return ydysyxz.getId();
    }

    @Override
    public YdysyxzRes get(String projectId) {
        Ydysyxz ydysyxz = ydysyxzMapper.get(projectId);
        Project project = projectMapper.get(projectId);

        YdysyxzRes ydysyxzRes = new YdysyxzRes();
        BeanUtils.copyProperties(ydysyxz, ydysyxzRes);
        ydysyxzRes.setProjectName(project.getName());
        ydysyxzRes.setProjectCode(project.getCode());
        ydysyxzRes.setProjectCompany(project.getCompany());
        return ydysyxzRes;
    }

    @Override
    public void update(YdysyxzUpdateVo ydysyxzUpdateVo) {
        ydysyxzMapper.update(ydysyxzUpdateVo);
    }
}
