package com.siwei.apply.domain.vo;

import lombok.Data;

/**
 * 项目过滤条件
 */
@Data
public class ProjectFilterVo {
    private String name;
    private String code;
    private Integer projectType;

    // 分页参数
    private Integer pageNum = 1; // 当前页码
    private Integer pageSize = 10; // 每页条数

    // ========== 计算属性（MyBatis 可直接调用 getOffset()） ==========
    /**
     * 计算偏移量（无需手动调用，MyBatis 会自动计算）
     */
    public Integer getOffset() {
        return (pageNum - 1) * pageSize;
    }

    // ========== 参数校验方法 ==========
    /**
     * 校验分页参数（避免非法值）
     */
    public void validatePageParams() {
        // 确保页码 >= 1
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        // 确保每页条数在 1~100 之间
        if (pageSize == null || pageSize < 1 || pageSize > 100) {
            pageSize = 10;
        }
    }
}
