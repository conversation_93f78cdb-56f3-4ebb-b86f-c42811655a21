package com.siwei.apply.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.siwei.common.core.web.domain.BaseSridEntity;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * 项目对象 t_project
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
@Data
public class Project {
    private String id;
    private String name;
    private String code;
    private String company;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateAt;
    private Integer projectType;
    private String creatorId;

    public void generateId() {
        this.id = UUID.randomUUID().toString();
    }
}
