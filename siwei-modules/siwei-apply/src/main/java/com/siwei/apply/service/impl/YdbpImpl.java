package com.siwei.apply.service.impl;

import com.siwei.apply.domain.Project;
import com.siwei.apply.domain.Ydbp;
import com.siwei.apply.domain.res.YdbpRes;
import com.siwei.apply.domain.vo.YdbpUpdateVo;
import com.siwei.apply.domain.vo.YdbpVo;
import com.siwei.apply.mapper.ProjectMapper;
import com.siwei.apply.mapper.YdbpMapper;
import com.siwei.apply.service.YdbpService;
import com.siwei.common.core.utils.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.siwei.apply.common.Common.UserId;

/**
 * 用地报批 服务实现类
 */
@Service
public class YdbpImpl implements YdbpService {
    @Autowired
    private YdbpMapper ydbpMapper;
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public Boolean isExit(String projectId) {
        return ydbpMapper.isExit(projectId);
    }

    @Override
    public String add(YdbpVo ydbpVo) {
        Ydbp ydbp = new Ydbp();
        BeanUtils.copyProperties(ydbpVo, ydbp);
        ydbp.generateId();
        ydbp.setCreatorId(UserId);
        ydbpMapper.add(ydbp);
        return ydbp.getId();
    }

    @Override
    public YdbpRes get(String projectId) {
        Ydbp ydbp = ydbpMapper.get(projectId);
        Project project = projectMapper.get(projectId);

        YdbpRes ydbpRes = new YdbpRes();
        BeanUtils.copyProperties(ydbp, ydbpRes);
        ydbpRes.setProjectName(project.getName());
        ydbpRes.setProjectCode(project.getCode());
        ydbpRes.setProjectType(project.getProjectType());
        return ydbpRes;
    }

    @Override
    public void update(YdbpUpdateVo ydbpUpdateVo) {
        ydbpMapper.update(ydbpUpdateVo);
    }
}
