package com.siwei.apply.service;

import com.siwei.apply.domain.Ydysyxz;
import com.siwei.apply.domain.res.YdysyxzRes;
import com.siwei.apply.domain.vo.YdysyxzUpdateVo;
import com.siwei.apply.domain.vo.YdysyxzVo;

/**
 * 用地预审与选址 服务接口
 */
public interface YdysyxzService {
    /**
     * 根据项目projectId查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加用地预审与选址信息
     */
    String add(YdysyxzVo ydysyxzVo);

    /**
     * 获取用地预审与选址信息
     *
     * @param projectId 项目ID
     * @return 用地预审与选址信息
     */
    YdysyxzRes get(String projectId);

    /**
     * 更新用地预审与选址信息
     *
     * @param ydysyxzUpdateVo 用地预审与选址视图对象
     */
    void update(YdysyxzUpdateVo ydysyxzUpdateVo);
}
