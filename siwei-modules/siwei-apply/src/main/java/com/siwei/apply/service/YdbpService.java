package com.siwei.apply.service;

import com.siwei.apply.domain.res.YdbpRes;
import com.siwei.apply.domain.vo.YdbpUpdateVo;
import com.siwei.apply.domain.vo.YdbpVo;

/**
 * 用地报批 服务接口
 */
public interface YdbpService {
    /**
     * 根据项目projectId查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加用地报批信息
     *
     * @param ydbpVo 用地报批视图对象
     * @return 用地报批ID
     */
    String add(YdbpVo ydbpVo);

    /**
     * 获取用地报批信息
     *
     * @param projectId 项目ID
     * @return 用地报批结果对象
     */
    YdbpRes get(String projectId);

    /**
     * 更新用地报批信息
     *
     * @param ydbpUpdateVo 用地报批更新视图对象
     */
    void update(YdbpUpdateVo ydbpUpdateVo);
}
