package com.siwei.apply.mapper;

import com.siwei.apply.domain.Ydysyxz;
import com.siwei.apply.domain.vo.YdysyxzUpdateVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用地预审与选址 t_ydysyxz
 */
@Mapper
public interface YdysyxzMapper {
    /**
     * 根据项目id查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加用地预审与选址信息
     *
     * @param ydysyxz
     */
    void add(Ydysyxz ydysyxz);

    /**
     * 获取用地预审与选址信息
     *
     * @param projectId
     * @return
     */
    Ydysyxz get(String projectId);

    /**
     * 更新用地预审与选址信息
     *
     * @param ydysyxzUpdateVo
     */
    void update(YdysyxzUpdateVo ydysyxzUpdateVo);
}
