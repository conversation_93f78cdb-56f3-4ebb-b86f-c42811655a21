package com.siwei.apply.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * 用地报批 对象 t_ydbp
 * 单独选址第二部分
 */
@Data
public class Ydbp {
    private String id;
    private String projectId;
    private String tdyt;//土地用途
    private Float ydArea;//用地面积
    private Float zsArea;//征收面积
    private Boolean hasZZ;//是否完成征转
    private String bpDate;//报批日期
    private String pfwh;//批复文号
    private String pfDate;//批复日期
    private Map<String, Object> attachment; // 存储附件的目录树
    private Boolean hasOnchain;//是否上链
    private String creatorId; // 创建人ID

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt; // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt; // 更新时间

    public void generateId() {
        this.id = UUID.randomUUID().toString();
    }
}
