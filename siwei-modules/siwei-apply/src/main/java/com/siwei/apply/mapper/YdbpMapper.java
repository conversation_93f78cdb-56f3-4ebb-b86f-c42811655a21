package com.siwei.apply.mapper;

import com.siwei.apply.domain.Ydbp;
import com.siwei.apply.domain.vo.YdbpUpdateVo;
import org.apache.ibatis.annotations.Mapper;

// 用地报批 Mapper 接口
@Mapper
public interface YdbpMapper {
    /**
     * 根据项目id查询是否存在
     */
    Boolean isExit(String projectId);

    /**
     * 添加用地报批信息
     *
     * @param ydbp
     */
    void add(Ydbp ydbp);

    /**
     * 获取用地报批信息
     *
     * @param projectId
     * @return
     */
    Ydbp get(String projectId);

    /**
     * 更新用地报批信息
     *
     * @param ydbpUpdateVo
     */
    void update(YdbpUpdateVo ydbpUpdateVo);
}
