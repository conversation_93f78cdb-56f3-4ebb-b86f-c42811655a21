当你在 GitHub 上 fork 了一个仓库，并且源仓库（上游仓库）有更新时，你可以通过以下步骤将源仓库的更新同步到你 fork 后的仓库中：

1. 设置上游仓库（源仓库）为远程仓库
首先，确保你已经将源仓库（上游仓库）添加为远程仓库。你可以通过 git remote -v 查看当前的远程仓库设置。

bash
复制
编辑
# 进入本地 fork 的仓库目录
cd your-forked-repo

# 查看当前远程仓库设置
git remote -v
如果没有设置上游仓库，可以通过以下命令添加：

bash
复制
编辑
# 添加上游仓库
git remote add upstream https://github.com/原作者/源仓库.git
2. 获取源仓库的更新
通过以下命令获取源仓库的最新更新：

bash
复制
编辑
# 从上游仓库获取最新的更新
git fetch upstream
此时，上游仓库的更改已经被拉取到你本地的 upstream 远程分支。

3. 合并源仓库的更新到你的分支
切换到你希望合并更新的本地分支，通常是 main 或 master 分支：

bash
复制
编辑
# 切换到主分支（假设为 main）
git checkout main
然后，将源仓库（upstream）的更新合并到你本地的分支：

bash
复制
编辑
# 合并上游仓库的更新到当前分支
git merge upstream/main
如果源仓库的主分支是 master，则替换为 upstream/master。

4. 解决冲突（如果有）
如果有冲突，Git 会提示你解决冲突。解决完冲突后，添加解决后的文件并提交：

bash
复制
编辑
# 添加解决冲突后的文件
git add .

# 提交
git commit -m "解决冲突，合并源仓库的更新"
5. 推送更新到 GitHub
最后，推送合并后的更改到你在 GitHub 上的 fork 仓库：

bash
复制
编辑
# 推送更新到 GitHub 上的 fork 仓库
git push origin main
总结
设置上游仓库（源仓库）为远程仓库：git remote add upstream <source-repo-url>
获取上游仓库的更新：git fetch upstream
合并更新到你的本地分支：git merge upstream/main
解决冲突（如果有）并提交
推送更新到 GitHub 上的 fork 仓库：git push origin main