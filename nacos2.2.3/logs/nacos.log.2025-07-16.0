2025-07-16 19:01:02,739 INFO The server IP list of Nacos is []

2025-07-16 19:01:02,744 INFO Starting Nacos v2.2.3 using Java 1.8.0_131 on CHINAMI-5O2JUPR with PID 31112 (D:\nacos2.2.3\target\nacos-server.jar started by Administrator in D:\nacos2.2.3\bin)

2025-07-16 19:01:02,745 INFO No active profile set, falling back to 1 default profile: "default"

2025-07-16 19:01:03,746 INFO Nacos is starting...

2025-07-16 19:01:04,754 INFO Nacos is starting...

2025-07-16 19:01:05,767 INFO Nacos is starting...

2025-07-16 19:01:06,038 INFO Tomcat initialized with port(s): 8848 (http)

2025-07-16 19:01:06,052 INFO Initializing ProtocolHandler ["http-nio-8848"]

2025-07-16 19:01:06,053 INFO Starting service [Tomcat]

2025-07-16 19:01:06,064 INFO Starting Servlet engine: [Apache Tomcat/9.0.69]

2025-07-16 19:01:06,205 INFO Initializing Spring embedded WebApplicationContext

2025-07-16 19:01:06,205 INFO Root WebApplicationContext: initialization completed in 3396 ms

2025-07-16 19:01:06,696 INFO Nacos-related cluster resource initialization

2025-07-16 19:01:06,703 INFO Load com.alibaba.nacos.core.ability.RemoteAbilityInitializer for ServerAbilityInitializer

2025-07-16 19:01:06,703 INFO Load com.alibaba.nacos.naming.ability.NamingAbilityInitializer for ServerAbilityInitializer

2025-07-16 19:01:06,707 INFO The cluster resource is initialized

2025-07-16 19:01:06,770 INFO Nacos is starting...

2025-07-16 19:01:07,782 INFO Nacos is starting...

2025-07-16 19:01:08,494 INFO HikariPool-1 - Starting...

2025-07-16 19:01:08,705 INFO HikariPool-1 - Start completed.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.postgresql.ConfigInfoAggrMapperByPostgresql) datasource(postgresql) tableName(config_info_aggr) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.postgresql.ConfigInfoBetaMapperByPostgresql) datasource(postgresql) tableName(config_info_beta) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.postgresql.ConfigInfoMapperByPostgresql) datasource(postgresql) tableName(config_info) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.postgresql.ConfigInfoTagMapperByPostgresql) datasource(postgresql) tableName(config_info_tag) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.postgresql.ConfigTagsRelationMapperByPostgresql) datasource(postgresql) tableName(config_tags_relation) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.postgresql.HistoryConfigInfoMapperByPostgresql) datasource(postgresql) tableName(his_config_info) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.postgresql.TenantInfoMapperByPostgresql) datasource(postgresql) tableName(tenant_info) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.postgresql.TenantCapacityMapperByPostgresql) datasource(postgresql) tableName(tenant_capacity) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.postgresql.GroupCapacityMapperByPostgresql) datasource(postgresql) tableName(group_capacity) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoAggrMapperByMySql) datasource(mysql) tableName(config_info_aggr) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoBetaMapperByMySql) datasource(mysql) tableName(config_info_beta) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoMapperByMySql) datasource(mysql) tableName(config_info) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoTagMapperByMySql) datasource(mysql) tableName(config_info_tag) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigTagsRelationMapperByMySql) datasource(mysql) tableName(config_tags_relation) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.HistoryConfigInfoMapperByMySql) datasource(mysql) tableName(his_config_info) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantInfoMapperByMySql) datasource(mysql) tableName(tenant_info) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantCapacityMapperByMySql) datasource(mysql) tableName(tenant_capacity) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.GroupCapacityMapperByMysql) datasource(mysql) tableName(group_capacity) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoAggrMapperByDerby) datasource(derby) tableName(config_info_aggr) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoBetaMapperByDerby) datasource(derby) tableName(config_info_beta) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoMapperByDerby) datasource(derby) tableName(config_info) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagMapperByDerby) datasource(derby) tableName(config_info_tag) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagsRelationMapperByDerby) datasource(derby) tableName(config_tags_relation) successfully.

2025-07-16 19:01:08,757 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.HistoryConfigInfoMapperByDerby) datasource(derby) tableName(his_config_info) successfully.

2025-07-16 19:01:08,758 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantInfoMapperByDerby) datasource(derby) tableName(tenant_info) successfully.

2025-07-16 19:01:08,758 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantCapacityMapperByDerby) datasource(derby) tableName(tenant_capacity) successfully.

2025-07-16 19:01:08,758 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.GroupCapacityMapperByDerby) datasource(derby) tableName(group_capacity) successfully.

2025-07-16 19:01:08,790 INFO Nacos is starting...

2025-07-16 19:01:09,265 WARN Fail to found tps control manager of name ：nacos

2025-07-16 19:01:09,267 WARN Fail to connection control manager of name ：nacos

2025-07-16 19:01:09,269 INFO Load connection metrics collector,size=2,[com.alibaba.nacos.config.server.service.LongPollingConnectionMetricsCollector@4fce136b, com.alibaba.nacos.core.remote.LongConnectionMetricsCollector@43b6123e]

2025-07-16 19:01:09,273 INFO No connection rule content found ,use default empty rule 

2025-07-16 19:01:09,279 INFO Fail to find connection runtime ejector for name nacos,use default

2025-07-16 19:01:09,283 INFO Found tps barrier creator of name : nacos

2025-07-16 19:01:09,286 WARN Fail to found tps rule creator of name : nacos,use  default local simple creator

2025-07-16 19:01:09,288 INFO No tps control rule of CONFIG_PUSH_COUNT found  

2025-07-16 19:01:09,289 INFO No tps control rule of CONFIG_PUSH_SUCCESS found  

2025-07-16 19:01:09,289 INFO No tps control rule of CONFIG_PUSH_FAIL found  

2025-07-16 19:01:09,354 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_info

2025-07-16 19:01:09,360 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_info

2025-07-16 19:01:09,407 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_info_aggr

2025-07-16 19:01:09,425 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-07-16 19:01:09,564 INFO Get Distro config from env, DistroConfig{syncDelayMillis=1000, syncTimeoutMillis=3000, syncRetryDelayMillis=3000, verifyIntervalMillis=5000, verifyTimeoutMillis=3000, loadDataRetryDelayMillis=30000}

2025-07-16 19:01:09,798 INFO Nacos is starting...

2025-07-16 19:01:10,295 INFO Connection check task start

2025-07-16 19:01:10,316 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-07-16 19:01:10,316 INFO Connection check task end

2025-07-16 19:01:10,809 INFO Nacos is starting...

2025-07-16 19:01:10,844 INFO Adding welcome page: class path resource [static/index.html]

2025-07-16 19:01:11,524 WARN You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-07-16 19:01:11,524 INFO Will not secure Ant [pattern='/**']

2025-07-16 19:01:11,547 INFO Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6d511b5f, org.springframework.security.web.context.SecurityContextPersistenceFilter@158d255c, org.springframework.security.web.header.HeaderWriterFilter@48f5bde6, org.springframework.security.web.csrf.CsrfFilter@5489c777, org.springframework.security.web.authentication.logout.LogoutFilter@50305a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@327120c8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2b5cb9b2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@41200e0c, org.springframework.security.web.session.SessionManagementFilter@5149f008, org.springframework.security.web.access.ExceptionTranslationFilter@62f87c44]

2025-07-16 19:01:11,574 INFO Exposing 1 endpoint(s) beneath base path '/actuator'

2025-07-16 19:01:11,603 INFO Starting ProtocolHandler ["http-nio-8848"]

2025-07-16 19:01:11,616 INFO Tomcat started on port(s): 8848 (http) with context path '/nacos'

2025-07-16 19:01:11,633 INFO No tps control rule of ConfigQuery found  

2025-07-16 19:01:11,633 INFO No tps control rule of ConfigPublish found  

2025-07-16 19:01:11,634 INFO No tps control rule of ClusterConfigChangeNotify found  

2025-07-16 19:01:11,634 INFO No tps control rule of ConfigListen found  

2025-07-16 19:01:11,634 INFO No tps control rule of ConfigRemove found  

2025-07-16 19:01:11,634 INFO No tps control rule of HealthCheck found  

2025-07-16 19:01:11,638 INFO Started Nacos in 9.906 seconds (JVM running for 10.526)

2025-07-16 19:01:11,639 INFO Nacos started successfully in cluster mode. use external storage

2025-07-16 19:01:12,440 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-07-16 19:01:12,957 INFO new connection registered successfully, connectionId = 1752663672887_127.0.0.1_58464,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='************', remoteIp='127.0.0.1', remotePort=58464, localPort=9848, version='Nacos-Java-Client:v2.0.4', connectionId='1752663672887_127.0.0.1_58464', createTime=Wed Jul 16 19:01:12 GMT+08:00 2025, lastActiveTime=1752663672940, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-07-16 19:01:12,958 INFO new connection registered successfully, connectionId = 1752663672910_127.0.0.1_58465,connection=Connection{traced=false, abilities=com.alibaba.nacos.api.ability.ClientAbilities@2ae74a9e, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='************', remoteIp='127.0.0.1', remotePort=58465, localPort=9848, version='Nacos-Java-Client:v2.0.4', connectionId='1752663672910_127.0.0.1_58465', createTime=Wed Jul 16 19:01:12 GMT+08:00 2025, lastActiveTime=1752663672940, appName='unknown', tenant='application', labels={source=sdk, taskId=0, module=config, AppName=unknown}}} 

2025-07-16 19:01:12,958 INFO new connection registered successfully, connectionId = 1752663672871_127.0.0.1_58462,connection=Connection{traced=false, abilities=com.alibaba.nacos.api.ability.ClientAbilities@580ec6cb, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='************', remoteIp='127.0.0.1', remotePort=58462, localPort=9848, version='Nacos-Java-Client:v2.0.4', connectionId='1752663672871_127.0.0.1_58462', createTime=Wed Jul 16 19:01:12 GMT+08:00 2025, lastActiveTime=1752663672940, appName='unknown', tenant='application', labels={source=sdk, taskId=0, module=config, AppName=unknown}}} 

2025-07-16 19:01:12,958 INFO new connection registered successfully, connectionId = 1752663672887_127.0.0.1_58463,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='************', remoteIp='127.0.0.1', remotePort=58463, localPort=9848, version='Nacos-Java-Client:v2.0.4', connectionId='1752663672887_127.0.0.1_58463', createTime=Wed Jul 16 19:01:12 GMT+08:00 2025, lastActiveTime=1752663672940, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-07-16 19:01:13,037 INFO [AuthPluginManager] Load AuthPluginService(class com.alibaba.nacos.plugin.auth.impl.NacosAuthPluginService) AuthServiceName(nacos) successfully.

2025-07-16 19:01:13,037 INFO [AuthPluginManager] Load AuthPluginService(class com.alibaba.nacos.plugin.auth.impl.LdapAuthPluginService) AuthServiceName(ldap) successfully.

2025-07-16 19:01:13,325 INFO Connection check task start

2025-07-16 19:01:13,325 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:13,326 INFO Connection check task end

2025-07-16 19:01:14,559 INFO Get Push config from env, PushConfig{pushTaskDelay=500, pushTaskTimeout=5000, pushTaskRetryDelay=1000}

2025-07-16 19:01:14,705 INFO Tps reporting...
ConfigListen|point|SECONDS|2025-07-16 19:01:13|2|0|


2025-07-16 19:01:15,291 INFO No tps control rule of NAMING_RPC_PUSH found  

2025-07-16 19:01:15,291 INFO No tps control rule of NAMING_RPC_PUSH_SUCCESS found  

2025-07-16 19:01:15,291 INFO No tps control rule of NAMING_RPC_PUSH_FAIL found  

2025-07-16 19:01:15,291 INFO No tps control rule of NAMING_UDP_PUSH found  

2025-07-16 19:01:15,291 INFO No tps control rule of NAMING_UDP_PUSH_SUCCESS found  

2025-07-16 19:01:15,292 INFO No tps control rule of NAMING_UDP_PUSH_FAIL found  

2025-07-16 19:01:15,292 INFO No tps control rule of NAMING_DISTRO_SYNC found  

2025-07-16 19:01:15,292 INFO No tps control rule of NAMING_DISTRO_SYNC_SUCCESS found  

2025-07-16 19:01:15,292 INFO No tps control rule of NAMING_DISTRO_SYNC_FAIL found  

2025-07-16 19:01:15,292 INFO No tps control rule of NAMING_DISTRO_VERIFY found  

2025-07-16 19:01:15,292 INFO No tps control rule of NAMING_DISTRO_VERIFY_SUCCESS found  

2025-07-16 19:01:15,292 INFO No tps control rule of NAMING_DISTRO_VERIFY_FAIL found  

2025-07-16 19:01:15,447 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:16,327 INFO Connection check task start

2025-07-16 19:01:16,327 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:16,327 INFO Connection check task end

2025-07-16 19:01:16,526 INFO Tps reporting...
NAMING_RPC_PUSH_SUCCESS|point|SECONDS|2025-07-16 19:01:15|1|0|
NAMING_RPC_PUSH|point|SECONDS|2025-07-16 19:01:15|1|0|


2025-07-16 19:01:17,439 INFO Tps reporting...
NAMING_RPC_PUSH_SUCCESS|point|SECONDS|2025-07-16 19:01:16|1|0|
NAMING_RPC_PUSH|point|SECONDS|2025-07-16 19:01:16|1|0|


2025-07-16 19:01:18,459 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:19,328 INFO Connection check task start

2025-07-16 19:01:19,328 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:19,328 INFO Connection check task end

2025-07-16 19:01:21,464 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:22,332 INFO Connection check task start

2025-07-16 19:01:22,332 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:22,332 INFO Connection check task end

2025-07-16 19:01:24,474 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:24,720 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:01:23|4|0|


2025-07-16 19:01:25,344 INFO Connection check task start

2025-07-16 19:01:25,344 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:25,344 INFO Connection check task end

2025-07-16 19:01:27,483 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:28,357 INFO Connection check task start

2025-07-16 19:01:28,357 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:28,357 INFO Connection check task end

2025-07-16 19:01:29,263 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:01:28|4|0|


2025-07-16 19:01:30,486 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:31,358 INFO Connection check task start

2025-07-16 19:01:31,358 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:31,358 INFO Connection check task end

2025-07-16 19:01:33,492 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:34,359 INFO Connection check task start

2025-07-16 19:01:34,359 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:34,359 INFO Connection check task end

2025-07-16 19:01:34,717 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:01:33|4|0|


2025-07-16 19:01:36,502 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:37,368 INFO Connection check task start

2025-07-16 19:01:37,368 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:37,368 INFO Connection check task end

2025-07-16 19:01:39,263 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:01:38|4|0|


2025-07-16 19:01:39,513 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:40,378 INFO Connection check task start

2025-07-16 19:01:40,378 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:40,378 INFO Connection check task end

2025-07-16 19:01:42,514 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:43,383 INFO Connection check task start

2025-07-16 19:01:43,383 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:43,383 INFO Connection check task end

2025-07-16 19:01:44,732 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:01:43|4|0|


2025-07-16 19:01:45,524 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:46,396 INFO Connection check task start

2025-07-16 19:01:46,396 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:46,396 INFO Connection check task end

2025-07-16 19:01:48,536 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:49,267 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:01:48|4|0|


2025-07-16 19:01:49,397 INFO Initializing Spring DispatcherServlet 'dispatcherServlet'

2025-07-16 19:01:49,397 INFO Initializing Servlet 'dispatcherServlet'

2025-07-16 19:01:49,399 INFO Completed initialization in 1 ms

2025-07-16 19:01:49,409 INFO Connection check task start

2025-07-16 19:01:49,409 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:49,409 INFO Connection check task end

2025-07-16 19:01:49,441 INFO [MapperManager] findMapper dataSource: postgresql, tableName: tenant_info

2025-07-16 19:01:49,444 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_info

2025-07-16 19:01:49,445 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_info

2025-07-16 19:01:49,576 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_info

2025-07-16 19:01:51,544 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:52,412 INFO Connection check task start

2025-07-16 19:01:52,412 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:52,412 INFO Connection check task end

2025-07-16 19:01:54,555 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:54,725 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:01:53|4|0|


2025-07-16 19:01:55,426 INFO Connection check task start

2025-07-16 19:01:55,426 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:55,426 INFO Connection check task end

2025-07-16 19:01:57,562 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:01:58,428 INFO Connection check task start

2025-07-16 19:01:58,428 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:01:58,428 INFO Connection check task end

2025-07-16 19:01:59,281 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:01:58|4|0|


2025-07-16 19:02:00,568 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:01,430 INFO Connection check task start

2025-07-16 19:02:01,430 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:01,430 INFO Connection check task end

2025-07-16 19:02:03,582 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:04,432 INFO Connection check task start

2025-07-16 19:02:04,432 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:04,432 INFO Connection check task end

2025-07-16 19:02:04,742 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:03|4|0|


2025-07-16 19:02:06,596 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:07,445 INFO Connection check task start

2025-07-16 19:02:07,445 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:07,445 INFO Connection check task end

2025-07-16 19:02:09,290 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:08|4|0|


2025-07-16 19:02:09,598 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:10,451 INFO Connection check task start

2025-07-16 19:02:10,451 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:10,451 INFO Connection check task end

2025-07-16 19:02:12,599 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:13,462 INFO Connection check task start

2025-07-16 19:02:13,462 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:13,462 INFO Connection check task end

2025-07-16 19:02:14,762 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:13|4|0|


2025-07-16 19:02:15,603 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:16,470 INFO Connection check task start

2025-07-16 19:02:16,470 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:16,470 INFO Connection check task end

2025-07-16 19:02:18,606 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:19,304 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:18|4|0|


2025-07-16 19:02:19,476 INFO Connection check task start

2025-07-16 19:02:19,476 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:19,476 INFO Connection check task end

2025-07-16 19:02:21,616 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:22,481 INFO Connection check task start

2025-07-16 19:02:22,481 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:22,481 INFO Connection check task end

2025-07-16 19:02:24,629 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:24,786 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:23|4|0|


2025-07-16 19:02:25,483 INFO Connection check task start

2025-07-16 19:02:25,483 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:25,483 INFO Connection check task end

2025-07-16 19:02:27,639 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:28,492 INFO Connection check task start

2025-07-16 19:02:28,492 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:28,492 INFO Connection check task end

2025-07-16 19:02:29,343 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:28|3|0|


2025-07-16 19:02:30,650 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:31,500 INFO Connection check task start

2025-07-16 19:02:31,500 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:31,500 INFO Connection check task end

2025-07-16 19:02:33,656 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:34,513 INFO Connection check task start

2025-07-16 19:02:34,513 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:34,513 INFO Connection check task end

2025-07-16 19:02:34,809 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:33|3|0|


2025-07-16 19:02:36,662 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:37,523 INFO Connection check task start

2025-07-16 19:02:37,523 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:37,523 INFO Connection check task end

2025-07-16 19:02:39,336 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:38|4|0|


2025-07-16 19:02:39,665 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:40,535 INFO Connection check task start

2025-07-16 19:02:40,535 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:40,535 INFO Connection check task end

2025-07-16 19:02:42,668 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:43,551 INFO Connection check task start

2025-07-16 19:02:43,551 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:43,551 INFO Connection check task end

2025-07-16 19:02:44,767 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:43|4|0|


2025-07-16 19:02:45,672 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:46,559 INFO Connection check task start

2025-07-16 19:02:46,559 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:46,559 INFO Connection check task end

2025-07-16 19:02:48,688 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:49,293 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:48|4|0|


2025-07-16 19:02:49,569 INFO Connection check task start

2025-07-16 19:02:49,569 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:49,569 INFO Connection check task end

2025-07-16 19:02:51,691 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:52,577 INFO Connection check task start

2025-07-16 19:02:52,577 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:52,577 INFO Connection check task end

2025-07-16 19:02:54,698 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:54,713 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:53|4|0|


2025-07-16 19:02:55,584 INFO Connection check task start

2025-07-16 19:02:55,584 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:55,584 INFO Connection check task end

2025-07-16 19:02:57,706 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:02:58,593 INFO Connection check task start

2025-07-16 19:02:58,593 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:02:58,593 INFO Connection check task end

2025-07-16 19:02:59,242 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:02:58|4|0|


2025-07-16 19:03:00,708 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:01,594 INFO Connection check task start

2025-07-16 19:03:01,594 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:01,594 INFO Connection check task end

2025-07-16 19:03:03,715 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:04,595 INFO Connection check task start

2025-07-16 19:03:04,595 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:04,595 INFO Connection check task end

2025-07-16 19:03:04,672 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:03|4|0|


2025-07-16 19:03:06,727 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:07,610 INFO Connection check task start

2025-07-16 19:03:07,610 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:07,610 INFO Connection check task end

2025-07-16 19:03:09,196 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:08|4|0|


2025-07-16 19:03:09,738 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:10,620 INFO Connection check task start

2025-07-16 19:03:10,620 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:10,620 INFO Connection check task end

2025-07-16 19:03:12,750 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:13,622 INFO Connection check task start

2025-07-16 19:03:13,622 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:13,622 INFO Connection check task end

2025-07-16 19:03:14,651 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:13|4|0|


2025-07-16 19:03:15,756 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:16,628 INFO Connection check task start

2025-07-16 19:03:16,628 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:16,628 INFO Connection check task end

2025-07-16 19:03:18,772 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:19,181 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:18|4|0|


2025-07-16 19:03:19,630 INFO Connection check task start

2025-07-16 19:03:19,630 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:19,630 INFO Connection check task end

2025-07-16 19:03:21,789 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:22,642 INFO Connection check task start

2025-07-16 19:03:22,642 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:22,642 INFO Connection check task end

2025-07-16 19:03:24,613 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:23|4|0|


2025-07-16 19:03:24,800 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:25,649 INFO Connection check task start

2025-07-16 19:03:25,649 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:25,649 INFO Connection check task end

2025-07-16 19:03:27,816 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:28,655 INFO Connection check task start

2025-07-16 19:03:28,655 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:28,655 INFO Connection check task end

2025-07-16 19:03:29,153 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:28|3|0|


2025-07-16 19:03:30,824 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:31,667 INFO Connection check task start

2025-07-16 19:03:31,667 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:31,667 INFO Connection check task end

2025-07-16 19:03:33,838 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:34,610 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:33|3|0|


2025-07-16 19:03:34,672 INFO Connection check task start

2025-07-16 19:03:34,672 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:34,672 INFO Connection check task end

2025-07-16 19:03:36,852 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:37,680 INFO Connection check task start

2025-07-16 19:03:37,680 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:37,680 INFO Connection check task end

2025-07-16 19:03:39,128 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:38|4|0|


2025-07-16 19:03:39,856 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:40,695 INFO Connection check task start

2025-07-16 19:03:40,695 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:40,695 INFO Connection check task end

2025-07-16 19:03:42,872 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:43,697 INFO Connection check task start

2025-07-16 19:03:43,697 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:43,697 INFO Connection check task end

2025-07-16 19:03:44,569 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:43|4|0|


2025-07-16 19:03:45,888 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:46,705 INFO Connection check task start

2025-07-16 19:03:46,705 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:46,705 INFO Connection check task end

2025-07-16 19:03:48,901 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:49,123 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:48|4|0|


2025-07-16 19:03:49,712 INFO Connection check task start

2025-07-16 19:03:49,712 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:49,712 INFO Connection check task end

2025-07-16 19:03:51,906 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:52,716 INFO Connection check task start

2025-07-16 19:03:52,716 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:52,716 INFO Connection check task end

2025-07-16 19:03:54,568 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:53|4|0|


2025-07-16 19:03:54,914 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:55,720 INFO Connection check task start

2025-07-16 19:03:55,720 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:55,720 INFO Connection check task end

2025-07-16 19:03:57,914 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:03:58,730 INFO Connection check task start

2025-07-16 19:03:58,730 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:03:58,730 INFO Connection check task end

2025-07-16 19:03:59,091 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:03:58|4|0|


2025-07-16 19:04:00,918 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:01,733 INFO Connection check task start

2025-07-16 19:04:01,735 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:01,735 INFO Connection check task end

2025-07-16 19:04:03,934 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:04,622 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:03|4|0|


2025-07-16 19:04:04,748 INFO Connection check task start

2025-07-16 19:04:04,748 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:04,748 INFO Connection check task end

2025-07-16 19:04:06,938 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:07,760 INFO Connection check task start

2025-07-16 19:04:07,760 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:07,760 INFO Connection check task end

2025-07-16 19:04:09,146 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:08|4|0|


2025-07-16 19:04:09,948 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:10,773 INFO Connection check task start

2025-07-16 19:04:10,773 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:10,773 INFO Connection check task end

2025-07-16 19:04:12,951 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:13,782 INFO Connection check task start

2025-07-16 19:04:13,782 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:13,782 INFO Connection check task end

2025-07-16 19:04:14,599 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:13|4|0|


2025-07-16 19:04:15,958 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:16,797 INFO Connection check task start

2025-07-16 19:04:16,797 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:16,797 INFO Connection check task end

2025-07-16 19:04:18,970 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:19,125 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:18|4|0|


2025-07-16 19:04:19,805 INFO Connection check task start

2025-07-16 19:04:19,805 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:19,805 INFO Connection check task end

2025-07-16 19:04:21,985 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:22,808 INFO Connection check task start

2025-07-16 19:04:22,808 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:22,808 INFO Connection check task end

2025-07-16 19:04:24,568 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:23|4|0|


2025-07-16 19:04:24,989 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:25,136 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_info

2025-07-16 19:04:25,809 INFO Connection check task start

2025-07-16 19:04:25,809 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:25,809 INFO Connection check task end

2025-07-16 19:04:28,001 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:28,823 INFO Connection check task start

2025-07-16 19:04:28,823 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:28,823 INFO Connection check task end

2025-07-16 19:04:29,104 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:28|3|0|


2025-07-16 19:04:31,015 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:31,834 INFO Connection check task start

2025-07-16 19:04:31,834 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:31,834 INFO Connection check task end

2025-07-16 19:04:34,017 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:34,530 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:33|3|0|


2025-07-16 19:04:34,840 INFO Connection check task start

2025-07-16 19:04:34,840 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:34,840 INFO Connection check task end

2025-07-16 19:04:37,031 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:37,851 INFO Connection check task start

2025-07-16 19:04:37,851 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:37,851 INFO Connection check task end

2025-07-16 19:04:39,048 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:38|4|0|


2025-07-16 19:04:40,037 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:40,864 INFO Connection check task start

2025-07-16 19:04:40,864 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:40,864 INFO Connection check task end

2025-07-16 19:04:43,050 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:43,877 INFO Connection check task start

2025-07-16 19:04:43,877 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:43,877 INFO Connection check task end

2025-07-16 19:04:44,493 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:43|4|0|


2025-07-16 19:04:46,055 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:46,882 INFO Connection check task start

2025-07-16 19:04:46,882 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:46,882 INFO Connection check task end

2025-07-16 19:04:49,031 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:48|4|0|


2025-07-16 19:04:49,062 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:49,883 INFO Connection check task start

2025-07-16 19:04:49,883 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:49,883 INFO Connection check task end

2025-07-16 19:04:52,067 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:52,326 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_info

2025-07-16 19:04:52,891 INFO Connection check task start

2025-07-16 19:04:52,891 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:52,891 INFO Connection check task end

2025-07-16 19:04:53,076 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_info

2025-07-16 19:04:54,489 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:53|4|0|


2025-07-16 19:04:55,082 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:55,905 INFO Connection check task start

2025-07-16 19:04:55,905 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:55,905 INFO Connection check task end

2025-07-16 19:04:58,092 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:04:58,915 INFO Connection check task start

2025-07-16 19:04:58,915 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:04:58,915 INFO Connection check task end

2025-07-16 19:04:59,026 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:04:58|4|0|


2025-07-16 19:05:01,100 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:01,919 INFO Connection check task start

2025-07-16 19:05:01,919 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:01,919 INFO Connection check task end

2025-07-16 19:05:04,108 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:04,479 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:03|4|0|


2025-07-16 19:05:04,929 INFO Connection check task start

2025-07-16 19:05:04,929 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:04,929 INFO Connection check task end

2025-07-16 19:05:07,113 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:07,935 INFO Connection check task start

2025-07-16 19:05:07,935 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:07,935 INFO Connection check task end

2025-07-16 19:05:09,035 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:08|4|0|


2025-07-16 19:05:10,122 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:10,943 INFO Connection check task start

2025-07-16 19:05:10,943 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:10,943 INFO Connection check task end

2025-07-16 19:05:13,136 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:13,945 INFO Connection check task start

2025-07-16 19:05:13,945 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:13,945 INFO Connection check task end

2025-07-16 19:05:14,476 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:13|4|0|


2025-07-16 19:05:16,139 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:16,953 INFO Connection check task start

2025-07-16 19:05:16,954 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:16,954 INFO Connection check task end

2025-07-16 19:05:19,026 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:18|4|0|


2025-07-16 19:05:19,152 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:19,954 INFO Connection check task start

2025-07-16 19:05:19,954 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:19,954 INFO Connection check task end

2025-07-16 19:05:22,165 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:22,970 INFO Connection check task start

2025-07-16 19:05:22,970 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:22,970 INFO Connection check task end

2025-07-16 19:05:24,461 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:23|4|0|


2025-07-16 19:05:25,175 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:25,979 INFO Connection check task start

2025-07-16 19:05:25,979 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:25,979 INFO Connection check task end

2025-07-16 19:05:28,187 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:28,982 INFO Connection check task start

2025-07-16 19:05:28,982 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:28,982 INFO Connection check task end

2025-07-16 19:05:29,900 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:28|3|0|


2025-07-16 19:05:31,202 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:31,993 INFO Connection check task start

2025-07-16 19:05:31,993 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:31,993 INFO Connection check task end

2025-07-16 19:05:34,203 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:34,451 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:33|3|0|


2025-07-16 19:05:35,008 INFO Connection check task start

2025-07-16 19:05:35,008 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:35,008 INFO Connection check task end

2025-07-16 19:05:37,207 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:38,013 INFO Connection check task start

2025-07-16 19:05:38,013 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:38,013 INFO Connection check task end

2025-07-16 19:05:39,862 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:38|4|0|


2025-07-16 19:05:40,220 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:41,028 INFO Connection check task start

2025-07-16 19:05:41,029 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:41,029 INFO Connection check task end

2025-07-16 19:05:43,231 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:44,035 INFO Connection check task start

2025-07-16 19:05:44,035 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:44,035 INFO Connection check task end

2025-07-16 19:05:44,407 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:43|4|0|


2025-07-16 19:05:46,236 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:47,045 INFO Connection check task start

2025-07-16 19:05:47,045 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:47,045 INFO Connection check task end

2025-07-16 19:05:49,252 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:49,859 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:48|4|0|


2025-07-16 19:05:50,046 INFO Connection check task start

2025-07-16 19:05:50,047 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:50,047 INFO Connection check task end

2025-07-16 19:05:52,267 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:53,055 INFO Connection check task start

2025-07-16 19:05:53,055 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:53,055 INFO Connection check task end

2025-07-16 19:05:54,388 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:53|4|0|


2025-07-16 19:05:55,280 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:56,068 INFO Connection check task start

2025-07-16 19:05:56,068 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:56,068 INFO Connection check task end

2025-07-16 19:05:58,283 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:05:59,078 INFO Connection check task start

2025-07-16 19:05:59,078 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:05:59,078 INFO Connection check task end

2025-07-16 19:05:59,852 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:05:58|4|0|


2025-07-16 19:06:01,285 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:02,079 INFO Connection check task start

2025-07-16 19:06:02,079 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:02,079 INFO Connection check task end

2025-07-16 19:06:04,288 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:04,382 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:03|4|0|


2025-07-16 19:06:05,089 INFO Connection check task start

2025-07-16 19:06:05,089 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:05,089 INFO Connection check task end

2025-07-16 19:06:07,295 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:08,104 INFO Connection check task start

2025-07-16 19:06:08,104 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:08,104 INFO Connection check task end

2025-07-16 19:06:09,819 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:08|4|0|


2025-07-16 19:06:10,305 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:11,115 INFO Connection check task start

2025-07-16 19:06:11,115 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:11,115 INFO Connection check task end

2025-07-16 19:06:13,309 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:14,121 INFO Connection check task start

2025-07-16 19:06:14,121 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:14,121 INFO Connection check task end

2025-07-16 19:06:14,338 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:13|2|0|
ConfigListen|point|SECONDS|2025-07-16 19:06:13|2|0|


2025-07-16 19:06:16,318 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:17,130 INFO Connection check task start

2025-07-16 19:06:17,130 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:17,130 INFO Connection check task end

2025-07-16 19:06:17,433 INFO new connection registered successfully, connectionId = 1752663977319_127.0.0.1_58946,connection=Connection{traced=false, abilities=com.alibaba.nacos.api.ability.ClientAbilities@2ec115ad, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='************', remoteIp='127.0.0.1', remotePort=58946, localPort=9848, version='Nacos-Java-Client:v2.2.0', connectionId='1752663977319_127.0.0.1_58946', createTime=Wed Jul 16 19:06:17 GMT+08:00 2025, lastActiveTime=1752663977433, appName='unknown', tenant='rsmonitoring', labels={source=sdk, taskId=0, module=config, AppName=unknown}}} 

2025-07-16 19:06:18,878 INFO Tps reporting...
ConfigQuery|point|SECONDS|2025-07-16 19:06:17|4|0|


2025-07-16 19:06:19,324 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-07-16 19:06:19,786 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:18|4|0|


2025-07-16 19:06:20,139 INFO Connection check task start

2025-07-16 19:06:20,139 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-07-16 19:06:20,139 INFO Connection check task end

2025-07-16 19:06:22,325 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-07-16 19:06:23,150 INFO Connection check task start

2025-07-16 19:06:23,150 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-07-16 19:06:23,150 INFO Connection check task end

2025-07-16 19:06:24,326 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:23|4|0|


2025-07-16 19:06:25,335 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-07-16 19:06:26,159 INFO Connection check task start

2025-07-16 19:06:26,159 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-07-16 19:06:26,159 INFO Connection check task end

2025-07-16 19:06:26,577 INFO [1752663977319_127.0.0.1_58946]Connection unregistered successfully. 

2025-07-16 19:06:28,351 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:29,174 INFO Connection check task start

2025-07-16 19:06:29,174 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:29,174 INFO Connection check task end

2025-07-16 19:06:29,748 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:28|2|0|


2025-07-16 19:06:30,661 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:29|1|0|


2025-07-16 19:06:31,360 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:32,186 INFO Connection check task start

2025-07-16 19:06:32,186 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:32,186 INFO Connection check task end

2025-07-16 19:06:34,283 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:33|3|0|


2025-07-16 19:06:34,375 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:35,196 INFO Connection check task start

2025-07-16 19:06:35,196 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:35,196 INFO Connection check task end

2025-07-16 19:06:37,377 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:38,200 INFO Connection check task start

2025-07-16 19:06:38,200 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:38,200 INFO Connection check task end

2025-07-16 19:06:39,748 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:38|3|0|


2025-07-16 19:06:40,379 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:40,658 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:39|1|0|


2025-07-16 19:06:41,215 INFO Connection check task start

2025-07-16 19:06:41,215 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:41,215 INFO Connection check task end

2025-07-16 19:06:43,394 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:44,220 INFO Connection check task start

2025-07-16 19:06:44,220 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:44,220 INFO Connection check task end

2025-07-16 19:06:44,297 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:43|2|0|


2025-07-16 19:06:45,211 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:44|2|0|


2025-07-16 19:06:46,407 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:47,225 INFO Connection check task start

2025-07-16 19:06:47,225 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:47,225 INFO Connection check task end

2025-07-16 19:06:49,422 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:50,227 INFO Connection check task start

2025-07-16 19:06:50,227 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:50,227 INFO Connection check task end

2025-07-16 19:06:50,642 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:49|4|0|


2025-07-16 19:06:52,432 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:53,238 INFO Connection check task start

2025-07-16 19:06:53,238 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:53,238 INFO Connection check task end

2025-07-16 19:06:55,180 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:54|4|0|


2025-07-16 19:06:55,443 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:56,252 INFO Connection check task start

2025-07-16 19:06:56,252 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:56,252 INFO Connection check task end

2025-07-16 19:06:58,454 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:06:59,258 INFO Connection check task start

2025-07-16 19:06:59,258 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:06:59,258 INFO Connection check task end

2025-07-16 19:07:00,613 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:06:59|4|0|


2025-07-16 19:07:01,213 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_info_beta

2025-07-16 19:07:01,257 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_tags_relation

2025-07-16 19:07:01,259 INFO [MapperManager] findMapper dataSource: postgresql, tableName: config_info

2025-07-16 19:07:01,463 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:02,272 INFO Connection check task start

2025-07-16 19:07:02,272 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:02,272 INFO Connection check task end

2025-07-16 19:07:04,475 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:05,158 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:04|4|0|


2025-07-16 19:07:05,282 INFO Connection check task start

2025-07-16 19:07:05,282 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:05,282 INFO Connection check task end

2025-07-16 19:07:07,484 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:08,288 INFO Connection check task start

2025-07-16 19:07:08,288 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:08,288 INFO Connection check task end

2025-07-16 19:07:10,492 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:10,600 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:09|4|0|


2025-07-16 19:07:11,299 INFO Connection check task start

2025-07-16 19:07:11,300 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:11,300 INFO Connection check task end

2025-07-16 19:07:13,497 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:14,308 INFO Connection check task start

2025-07-16 19:07:14,308 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:14,308 INFO Connection check task end

2025-07-16 19:07:15,130 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:14|4|0|


2025-07-16 19:07:16,500 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:17,310 INFO Connection check task start

2025-07-16 19:07:17,310 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:17,310 INFO Connection check task end

2025-07-16 19:07:19,513 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:20,323 INFO Connection check task start

2025-07-16 19:07:20,323 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:20,323 INFO Connection check task end

2025-07-16 19:07:20,574 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:19|4|0|


2025-07-16 19:07:22,529 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:23,327 INFO Connection check task start

2025-07-16 19:07:23,327 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:23,327 INFO Connection check task end

2025-07-16 19:07:25,101 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:24|4|0|


2025-07-16 19:07:25,539 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:26,337 INFO Connection check task start

2025-07-16 19:07:26,337 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:26,337 INFO Connection check task end

2025-07-16 19:07:28,550 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:29,346 INFO Connection check task start

2025-07-16 19:07:29,346 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:29,346 INFO Connection check task end

2025-07-16 19:07:30,541 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:29|3|0|


2025-07-16 19:07:31,564 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:32,357 INFO Connection check task start

2025-07-16 19:07:32,357 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:32,357 INFO Connection check task end

2025-07-16 19:07:34,565 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:35,084 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:34|3|0|


2025-07-16 19:07:35,370 INFO Connection check task start

2025-07-16 19:07:35,370 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:35,370 INFO Connection check task end

2025-07-16 19:07:37,569 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:07:38,378 INFO Connection check task start

2025-07-16 19:07:38,378 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:07:38,378 INFO Connection check task end

2025-07-16 19:07:40,000 INFO new connection registered successfully, connectionId = 1752664059897_127.0.0.1_59225,connection=Connection{traced=false, abilities=com.alibaba.nacos.api.ability.ClientAbilities@7cd5a480, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='************', remoteIp='127.0.0.1', remotePort=59225, localPort=9848, version='Nacos-Java-Client:v2.2.0', connectionId='1752664059897_127.0.0.1_59225', createTime=Wed Jul 16 19:07:40 GMT+08:00 2025, lastActiveTime=1752664060000, appName='unknown', tenant='rsmonitoring', labels={source=sdk, taskId=0, module=config, AppName=unknown}}} 

2025-07-16 19:07:40,533 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:39|4|0|


2025-07-16 19:07:40,579 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-07-16 19:07:41,381 INFO Connection check task start

2025-07-16 19:07:41,381 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-07-16 19:07:41,381 INFO Connection check task end

2025-07-16 19:07:41,443 INFO Tps reporting...
ConfigQuery|point|SECONDS|2025-07-16 19:07:40|4|0|


2025-07-16 19:07:43,591 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-07-16 19:07:44,390 INFO Connection check task start

2025-07-16 19:07:44,390 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-07-16 19:07:44,390 INFO Connection check task end

2025-07-16 19:07:45,060 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:44|4|0|


2025-07-16 19:07:46,602 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-07-16 19:07:47,130 INFO new connection registered successfully, connectionId = 1752664067117_127.0.0.1_59236,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='************', remoteIp='127.0.0.1', remotePort=59236, localPort=9848, version='Nacos-Java-Client:v2.2.0', connectionId='1752664067117_127.0.0.1_59236', createTime=Wed Jul 16 19:07:47 GMT+08:00 2025, lastActiveTime=1752664067130, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-07-16 19:07:47,396 INFO Connection check task start

2025-07-16 19:07:47,396 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:07:47,396 INFO Connection check task end

2025-07-16 19:07:48,677 INFO Tps reporting...
ConfigListen|point|SECONDS|2025-07-16 19:07:47|2|0|


2025-07-16 19:07:49,605 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:07:50,411 INFO Connection check task start

2025-07-16 19:07:50,411 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:07:50,411 INFO Connection check task end

2025-07-16 19:07:50,503 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:49|4|0|


2025-07-16 19:07:52,608 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:07:53,418 INFO Connection check task start

2025-07-16 19:07:53,419 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:07:53,419 INFO Connection check task end

2025-07-16 19:07:55,039 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:54|5|0|


2025-07-16 19:07:55,613 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:07:56,424 INFO Connection check task start

2025-07-16 19:07:56,424 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:07:56,424 INFO Connection check task end

2025-07-16 19:07:58,623 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:07:58,685 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:57|1|0|


2025-07-16 19:07:59,433 INFO Connection check task start

2025-07-16 19:07:59,433 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:07:59,433 INFO Connection check task end

2025-07-16 19:08:00,504 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:07:59|5|0|


2025-07-16 19:08:01,638 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:02,447 INFO Connection check task start

2025-07-16 19:08:02,447 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:02,447 INFO Connection check task end

2025-07-16 19:08:03,210 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:02|1|0|


2025-07-16 19:08:04,647 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:05,021 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:04|5|0|


2025-07-16 19:08:05,459 INFO Connection check task start

2025-07-16 19:08:05,459 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:05,459 INFO Connection check task end

2025-07-16 19:08:07,653 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:08,473 INFO Connection check task start

2025-07-16 19:08:08,473 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:08,473 INFO Connection check task end

2025-07-16 19:08:08,643 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:07|1|0|


2025-07-16 19:08:10,468 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:09|5|0|


2025-07-16 19:08:10,656 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:11,483 INFO Connection check task start

2025-07-16 19:08:11,483 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:11,483 INFO Connection check task end

2025-07-16 19:08:13,189 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:12|1|0|


2025-07-16 19:08:13,657 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:14,484 INFO Connection check task start

2025-07-16 19:08:14,484 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:14,484 INFO Connection check task end

2025-07-16 19:08:15,000 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:14|5|0|


2025-07-16 19:08:16,662 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:17,493 INFO Connection check task start

2025-07-16 19:08:17,493 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:17,493 INFO Connection check task end

2025-07-16 19:08:18,628 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:17|1|0|


2025-07-16 19:08:19,665 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:20,453 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:19|5|0|


2025-07-16 19:08:20,500 INFO Connection check task start

2025-07-16 19:08:20,500 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:20,500 INFO Connection check task end

2025-07-16 19:08:22,669 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:23,183 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:22|1|0|


2025-07-16 19:08:23,507 INFO Connection check task start

2025-07-16 19:08:23,508 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:23,508 INFO Connection check task end

2025-07-16 19:08:25,008 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:24|5|0|


2025-07-16 19:08:25,673 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:26,509 INFO Connection check task start

2025-07-16 19:08:26,509 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:26,509 INFO Connection check task end

2025-07-16 19:08:28,625 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:27|1|0|


2025-07-16 19:08:28,687 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:29,511 INFO Connection check task start

2025-07-16 19:08:29,511 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:29,511 INFO Connection check task end

2025-07-16 19:08:30,439 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:29|4|0|


2025-07-16 19:08:31,696 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:32,521 INFO Connection check task start

2025-07-16 19:08:32,521 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:32,521 INFO Connection check task end

2025-07-16 19:08:33,163 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:32|1|0|


2025-07-16 19:08:34,709 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:35,532 INFO Connection check task start

2025-07-16 19:08:35,532 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:35,532 INFO Connection check task end

2025-07-16 19:08:35,906 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:34|4|0|


2025-07-16 19:08:37,711 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:38,546 INFO Connection check task start

2025-07-16 19:08:38,546 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:38,546 INFO Connection check task end

2025-07-16 19:08:38,622 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:37|1|0|


2025-07-16 19:08:40,433 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:39|5|0|


2025-07-16 19:08:40,714 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:41,553 INFO Connection check task start

2025-07-16 19:08:41,553 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:41,553 INFO Connection check task end

2025-07-16 19:08:43,147 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:42|1|0|


2025-07-16 19:08:43,724 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:44,559 INFO Connection check task start

2025-07-16 19:08:44,559 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:44,559 INFO Connection check task end

2025-07-16 19:08:45,863 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:44|5|0|


2025-07-16 19:08:46,733 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:47,571 INFO Connection check task start

2025-07-16 19:08:47,571 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:47,571 INFO Connection check task end

2025-07-16 19:08:48,583 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:47|1|0|


2025-07-16 19:08:49,743 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:50,410 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:49|5|0|


2025-07-16 19:08:50,582 INFO Connection check task start

2025-07-16 19:08:50,582 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:50,582 INFO Connection check task end

2025-07-16 19:08:52,754 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:53,142 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:52|1|0|


2025-07-16 19:08:53,591 INFO Connection check task start

2025-07-16 19:08:53,591 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:53,591 INFO Connection check task end

2025-07-16 19:08:55,765 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:55,859 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:54|5|0|


2025-07-16 19:08:56,604 INFO Connection check task start

2025-07-16 19:08:56,604 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:56,604 INFO Connection check task end

2025-07-16 19:08:58,598 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:57|1|0|


2025-07-16 19:08:58,770 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:08:59,612 INFO Connection check task start

2025-07-16 19:08:59,612 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:08:59,612 INFO Connection check task end

2025-07-16 19:09:00,405 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:08:59|5|0|


2025-07-16 19:09:01,777 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:09:02,614 INFO Connection check task start

2025-07-16 19:09:02,614 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:09:02,614 INFO Connection check task end

2025-07-16 19:09:03,116 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:09:02|1|0|


2025-07-16 19:09:04,789 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-07-16 19:09:05,616 INFO Connection check task start

2025-07-16 19:09:05,616 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-07-16 19:09:05,616 INFO Connection check task end

2025-07-16 19:09:05,835 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:09:04|5|0|


2025-07-16 19:09:07,456 INFO [1752664067117_127.0.0.1_59236]Connection unregistered successfully. 

2025-07-16 19:09:07,792 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-07-16 19:09:08,001 INFO [1752664059897_127.0.0.1_59225]Connection unregistered successfully. 

2025-07-16 19:09:08,557 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:09:07|1|0|


2025-07-16 19:09:08,620 INFO Connection check task start

2025-07-16 19:09:08,620 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:09:08,620 INFO Connection check task end

2025-07-16 19:09:10,372 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:09:09|4|0|


2025-07-16 19:09:10,792 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:09:11,628 INFO Connection check task start

2025-07-16 19:09:11,629 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:09:11,629 INFO Connection check task end

2025-07-16 19:09:13,797 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:09:14,631 INFO Connection check task start

2025-07-16 19:09:14,631 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:09:14,631 INFO Connection check task end

2025-07-16 19:09:15,823 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:09:14|4|0|


2025-07-16 19:09:16,799 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:09:17,636 INFO Connection check task start

2025-07-16 19:09:17,636 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:09:17,636 INFO Connection check task end

2025-07-16 19:09:19,826 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:09:20,373 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:09:19|4|0|


2025-07-16 19:09:20,638 INFO Connection check task start

2025-07-16 19:09:20,638 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:09:20,638 INFO Connection check task end

2025-07-16 19:09:22,829 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:09:23,652 INFO Connection check task start

2025-07-16 19:09:23,652 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:09:23,652 INFO Connection check task end

2025-07-16 19:09:25,802 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:09:24|4|0|


2025-07-16 19:09:25,833 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:09:26,657 INFO Connection check task start

2025-07-16 19:09:26,657 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:09:26,657 INFO Connection check task end

2025-07-16 19:09:28,838 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:09:29,661 INFO Connection check task start

2025-07-16 19:09:29,661 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:09:29,661 INFO Connection check task end

2025-07-16 19:09:30,347 INFO Tps reporting...
HealthCheck|point|SECONDS|2025-07-16 19:09:29|3|0|


2025-07-16 19:09:31,839 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-07-16 19:09:32,673 INFO Connection check task start

2025-07-16 19:09:32,673 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-07-16 19:09:32,673 INFO Connection check task end

2025-07-16 19:09:34,853 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

