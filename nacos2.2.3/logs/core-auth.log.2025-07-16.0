2025-07-16 19:01:13,029 DEBUG auth start, request: ConfigBatchListenRequest

2025-07-16 19:01:13,040 DEBUG auth start, request: ConfigBatchListenRequest

2025-07-16 19:01:13,041 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='', name='', type='config', properties={requestClass=ConfigBatchListenRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:01:13,041 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='', name='', type='config', properties={requestClass=ConfigBatchListenRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:01:14,340 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:01:14,341 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-apply', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:01:14,549 DEBUG auth start, request: InstanceRequest

2025-07-16 19:01:14,550 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-apply', type='naming', properties={requestClass=InstanceRequest, action=w}}', action='w'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:01:14,570 DEBUG auth start, request: SubscribeServiceRequest

2025-07-16 19:01:14,572 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-apply', type='naming', properties={requestClass=SubscribeServiceRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:01:15,777 DEBUG auth start, request: InstanceRequest

2025-07-16 19:01:15,778 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-file', type='naming', properties={requestClass=InstanceRequest, action=w}}', action='w'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:01:15,788 DEBUG auth start, request: SubscribeServiceRequest

2025-07-16 19:01:15,789 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-file', type='naming', properties={requestClass=SubscribeServiceRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:01:49,522 DEBUG auth start, request: GET /nacos/v1/cs/configs

2025-07-16 19:01:49,541 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='', name='', type='config', properties={action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTU2Mn0.BD33SFduXbqpC-4uTdXAXl1vgHFj9DvKZyXrO5DdtL8', globalAdmin=false}

2025-07-16 19:02:25,534 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:02:25,534 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-file', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:02:30,401 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:02:30,402 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-apply', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:03:25,544 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:03:25,545 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-file', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:03:30,423 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:03:30,423 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-apply', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:04:25,133 DEBUG auth start, request: GET /nacos/v1/cs/configs

2025-07-16 19:04:25,134 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='', name='', type='config', properties={action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTU2Mn0.BD33SFduXbqpC-4uTdXAXl1vgHFj9DvKZyXrO5DdtL8', globalAdmin=false}

2025-07-16 19:04:25,566 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:04:25,566 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-file', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:04:30,431 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:04:30,432 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-apply', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:04:52,321 DEBUG auth start, request: GET /nacos/v1/cs/configs

2025-07-16 19:04:52,323 DEBUG auth permission: Permission{resource='Resource{namespaceId='', group='', name='', type='config', properties={action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTU2Mn0.BD33SFduXbqpC-4uTdXAXl1vgHFj9DvKZyXrO5DdtL8', globalAdmin=false}

2025-07-16 19:04:53,073 DEBUG auth start, request: GET /nacos/v1/cs/configs

2025-07-16 19:04:53,074 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='', name='', type='config', properties={action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTU2Mn0.BD33SFduXbqpC-4uTdXAXl1vgHFj9DvKZyXrO5DdtL8', globalAdmin=false}

2025-07-16 19:05:25,580 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:05:25,580 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-file', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:05:30,447 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:05:30,448 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-apply', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:06:13,514 DEBUG auth start, request: ConfigBatchListenRequest

2025-07-16 19:06:13,514 DEBUG auth start, request: ConfigBatchListenRequest

2025-07-16 19:06:13,514 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='', name='', type='config', properties={requestClass=ConfigBatchListenRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:06:13,514 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='', name='', type='config', properties={requestClass=ConfigBatchListenRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:06:17,605 DEBUG auth start, request: ConfigQueryRequest

2025-07-16 19:06:17,606 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='application-dev.yml', type='config', properties={requestClass=ConfigQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTk3Nn0.NivmO0vtHtnawXS5LgIDtp7yub4UVSJ7IUK-hq89oOI', globalAdmin=false}

2025-07-16 19:06:17,639 DEBUG auth start, request: ConfigQueryRequest

2025-07-16 19:06:17,640 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='siwei-spatial', type='config', properties={requestClass=ConfigQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTk3Nn0.NivmO0vtHtnawXS5LgIDtp7yub4UVSJ7IUK-hq89oOI', globalAdmin=false}

2025-07-16 19:06:17,649 DEBUG auth start, request: ConfigQueryRequest

2025-07-16 19:06:17,649 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='siwei-spatial.yml', type='config', properties={requestClass=ConfigQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTk3Nn0.NivmO0vtHtnawXS5LgIDtp7yub4UVSJ7IUK-hq89oOI', globalAdmin=false}

2025-07-16 19:06:17,658 DEBUG auth start, request: ConfigQueryRequest

2025-07-16 19:06:17,658 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='siwei-spatial-dev.yml', type='config', properties={requestClass=ConfigQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTk3Nn0.NivmO0vtHtnawXS5LgIDtp7yub4UVSJ7IUK-hq89oOI', globalAdmin=false}

2025-07-16 19:06:25,600 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:06:25,601 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-file', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:06:30,462 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:06:30,462 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-apply', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:07:01,208 DEBUG auth start, request: GET /nacos/v1/cs/configs

2025-07-16 19:07:01,208 DEBUG auth start, request: GET /nacos/v1/cs/configs/listener

2025-07-16 19:07:01,209 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='siwei-spatial-dev.yml', type='config', properties={action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTU2Mn0.BD33SFduXbqpC-4uTdXAXl1vgHFj9DvKZyXrO5DdtL8', globalAdmin=false}

2025-07-16 19:07:01,209 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='siwei-spatial-dev.yml', type='config', properties={action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTU2Mn0.BD33SFduXbqpC-4uTdXAXl1vgHFj9DvKZyXrO5DdtL8', globalAdmin=false}

2025-07-16 19:07:01,248 DEBUG auth start, request: GET /nacos/v1/cs/configs

2025-07-16 19:07:01,248 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='siwei-spatial-dev.yml', type='config', properties={action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTU2Mn0.BD33SFduXbqpC-4uTdXAXl1vgHFj9DvKZyXrO5DdtL8', globalAdmin=false}

2025-07-16 19:07:25,621 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:07:25,623 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-file', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:07:30,481 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:07:30,482 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-apply', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:07:40,168 DEBUG auth start, request: ConfigQueryRequest

2025-07-16 19:07:40,168 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='application-dev.yml', type='config', properties={requestClass=ConfigQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MjA1OX0.fwnF0ZYxhISd8BmxrDeailYzAbOnpj8tYHDeXa-OJU8', globalAdmin=false}

2025-07-16 19:07:40,189 DEBUG auth start, request: ConfigQueryRequest

2025-07-16 19:07:40,190 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='siwei-spatial', type='config', properties={requestClass=ConfigQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MjA1OX0.fwnF0ZYxhISd8BmxrDeailYzAbOnpj8tYHDeXa-OJU8', globalAdmin=false}

2025-07-16 19:07:40,209 DEBUG auth start, request: ConfigQueryRequest

2025-07-16 19:07:40,209 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='siwei-spatial.yml', type='config', properties={requestClass=ConfigQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MjA1OX0.fwnF0ZYxhISd8BmxrDeailYzAbOnpj8tYHDeXa-OJU8', globalAdmin=false}

2025-07-16 19:07:40,216 DEBUG auth start, request: ConfigQueryRequest

2025-07-16 19:07:40,217 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='siwei-spatial-dev.yml', type='config', properties={requestClass=ConfigQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MjA1OX0.fwnF0ZYxhISd8BmxrDeailYzAbOnpj8tYHDeXa-OJU8', globalAdmin=false}

2025-07-16 19:07:47,260 DEBUG auth start, request: InstanceRequest

2025-07-16 19:07:47,260 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='siwei-spatial', type='naming', properties={requestClass=InstanceRequest, action=w}}', action='w'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MjA2N30.WQNFtywD43BLT1EPNLfR9e6un79IZMaeSZS-pN_hNDA', globalAdmin=false}

2025-07-16 19:07:47,470 DEBUG auth start, request: ConfigBatchListenRequest

2025-07-16 19:07:47,470 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='', name='', type='config', properties={requestClass=ConfigBatchListenRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MjA1OX0.fwnF0ZYxhISd8BmxrDeailYzAbOnpj8tYHDeXa-OJU8', globalAdmin=false}

2025-07-16 19:07:47,485 DEBUG auth start, request: ConfigBatchListenRequest

2025-07-16 19:07:47,486 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='', name='', type='config', properties={requestClass=ConfigBatchListenRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MjA1OX0.fwnF0ZYxhISd8BmxrDeailYzAbOnpj8tYHDeXa-OJU8', globalAdmin=false}

2025-07-16 19:07:48,375 DEBUG auth start, request: ServiceListRequest

2025-07-16 19:07:48,375 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='', type='naming', properties={requestClass=ServiceListRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MjA2N30.WQNFtywD43BLT1EPNLfR9e6un79IZMaeSZS-pN_hNDA', globalAdmin=false}

2025-07-16 19:08:25,644 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:08:25,645 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-file', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:08:30,503 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:08:30,504 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-apply', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:09:07,439 DEBUG auth start, request: InstanceRequest

2025-07-16 19:09:07,439 DEBUG auth permission: Permission{resource='Resource{namespaceId='rsmonitoring', group='DEFAULT_GROUP', name='siwei-spatial', type='naming', properties={requestClass=InstanceRequest, action=w}}', action='w'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MjA2N30.WQNFtywD43BLT1EPNLfR9e6un79IZMaeSZS-pN_hNDA', globalAdmin=false}

2025-07-16 19:09:25,664 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:09:25,665 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-file', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

2025-07-16 19:09:30,520 DEBUG auth start, request: ServiceQueryRequest

2025-07-16 19:09:30,520 DEBUG auth permission: Permission{resource='Resource{namespaceId='application', group='DEFAULT_GROUP', name='onemap-apply', type='naming', properties={requestClass=ServiceQueryRequest, action=r}}', action='r'}, nacosUser: NacosUser{token='eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTc1MjY4MTUxNn0.IFlvl_ZRf5zF-oylrXuHFVqDkqXnf0MsLHE5vprpxO4', globalAdmin=false}

