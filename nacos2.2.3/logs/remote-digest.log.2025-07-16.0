2025-07-16 19:01:10,607 INFO Connection transportReady,connectionId = 1752663670607_127.0.0.1_58455 

2025-07-16 19:01:10,607 INFO Connection transportReady,connectionId = 1752663670607_127.0.0.1_58457 

2025-07-16 19:01:10,607 INFO Connection transportReady,connectionId = 1752663670607_127.0.0.1_58456 

2025-07-16 19:01:10,665 INFO Connection transportTerminated,connectionId = 1752663670607_127.0.0.1_58455 

2025-07-16 19:01:10,667 INFO Connection transportTerminated,connectionId = 1752663670607_127.0.0.1_58456 

2025-07-16 19:01:10,668 INFO Connection transportReady,connectionId = 1752663670667_127.0.0.1_58458 

2025-07-16 19:01:10,668 INFO Connection transportTerminated,connectionId = 1752663670607_127.0.0.1_58457 

2025-07-16 19:01:10,677 INFO Connection transportTerminated,connectionId = 1752663670667_127.0.0.1_58458 

2025-07-16 19:01:12,871 INFO Connection transportReady,connectionId = 1752663672871_127.0.0.1_58462 

2025-07-16 19:01:12,887 INFO Connection transportReady,connectionId = 1752663672887_127.0.0.1_58463 

2025-07-16 19:01:12,887 INFO Connection transportReady,connectionId = 1752663672887_127.0.0.1_58464 

2025-07-16 19:01:12,910 INFO Connection transportReady,connectionId = 1752663672910_127.0.0.1_58465 

2025-07-16 19:06:17,320 INFO Connection transportReady,connectionId = 1752663977319_127.0.0.1_58946 

2025-07-16 19:06:26,573 INFO Connection transportTerminated,connectionId = 1752663977319_127.0.0.1_58946 

2025-07-16 19:06:26,578 INFO [1752663977319_127.0.0.1_58946]client disconnected,clear config listen context

2025-07-16 19:07:39,897 INFO Connection transportReady,connectionId = 1752664059897_127.0.0.1_59225 

2025-07-16 19:07:47,117 INFO Connection transportReady,connectionId = 1752664067117_127.0.0.1_59236 

2025-07-16 19:09:07,455 INFO Connection transportTerminated,connectionId = 1752664067117_127.0.0.1_59236 

2025-07-16 19:09:07,456 WARN [1752664067117_127.0.0.1_59236] connection  close exception  : {}

java.lang.IllegalStateException: call already closed
	at com.google.common.base.Preconditions.checkState(Preconditions.java:508)
	at io.grpc.internal.ServerCallImpl.closeInternal(ServerCallImpl.java:219)
	at io.grpc.internal.ServerCallImpl.close(ServerCallImpl.java:212)
	at io.grpc.stub.ServerCalls$ServerCallStreamObserverImpl.onCompleted(ServerCalls.java:395)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.closeBiStream(GrpcConnection.java:143)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.close(GrpcConnection.java:131)
	at com.alibaba.nacos.core.remote.ConnectionManager.unregister(ConnectionManager.java:159)
	at com.alibaba.nacos.core.remote.grpc.AddressTransportFilter.transportTerminated(AddressTransportFilter.java:77)
	at io.grpc.internal.ServerImpl$ServerTransportListenerImpl.transportTerminated(ServerImpl.java:455)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.notifyTerminated(NettyServerTransport.java:207)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.access$100(NettyServerTransport.java:51)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:141)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:134)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:571)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:550)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:605)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:104)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$CloseFuture.setClosed(AbstractChannel.java:1164)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.doClose0(AbstractChannel.java:755)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:731)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:620)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.closeOnRead(AbstractNioByteChannel.java:105)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:174)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:722)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:658)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:584)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:496)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-07-16 19:09:07,456 INFO [1752664067117_127.0.0.1_59236]client disconnected,clear config listen context

2025-07-16 19:09:08,001 INFO Connection transportTerminated,connectionId = 1752664059897_127.0.0.1_59225 

2025-07-16 19:09:08,001 INFO [1752664059897_127.0.0.1_59225]client disconnected,clear config listen context

