2025/05/13-14:52:50.459460 ad70 RocksDB version: 7.7.3
2025/05/13-14:52:50.459533 ad70 Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/05/13-14:52:50.459566 ad70 Compile date 2022-10-24 17:17:55
2025/05/13-14:52:50.459589 ad70 DB SUMMARY
2025/05/13-14:52:50.459611 ad70 DB Session ID:  YQ48OGY1SY7MI8000H6D
2025/05/13-14:52:50.460160 ad70 CURRENT file:  CURRENT
2025/05/13-14:52:50.460191 ad70 IDENTITY file:  IDENTITY
2025/05/13-14:52:50.460257 ad70 MANIFEST file:  MANIFEST-000019 size: 524 Bytes
2025/05/13-14:52:50.460299 ad70 SST files in D:\soft\nacos-server-2.2.3\nacos\data\protocol\raft\naming_persistent_service_v2\log dir, Total Num: 4, files: 000010.sst 000011.sst 000016.sst 000017.sst 
2025/05/13-14:52:50.460320 ad70 Write Ahead Log file in D:\soft\nacos-server-2.2.3\nacos\data\protocol\raft\naming_persistent_service_v2\log: 000018.log size: 313 ; 
2025/05/13-14:52:50.460890 ad70                         Options.error_if_exists: 0
2025/05/13-14:52:50.460934 ad70                       Options.create_if_missing: 1
2025/05/13-14:52:50.460950 ad70                         Options.paranoid_checks: 1
2025/05/13-14:52:50.460968 ad70             Options.flush_verify_memtable_count: 1
2025/05/13-14:52:50.460983 ad70                               Options.track_and_verify_wals_in_manifest: 0
2025/05/13-14:52:50.460997 ad70        Options.verify_sst_unique_id_in_manifest: 1
2025/05/13-14:52:50.461011 ad70                                     Options.env: 000002B07A440270
2025/05/13-14:52:50.461027 ad70                                      Options.fs: WinFS
2025/05/13-14:52:50.461043 ad70                                Options.info_log: 000002B0053F1F90
2025/05/13-14:52:50.461057 ad70                Options.max_file_opening_threads: 16
2025/05/13-14:52:50.461071 ad70                              Options.statistics: 000002B07A5D0220
2025/05/13-14:52:50.461116 ad70                               Options.use_fsync: 0
2025/05/13-14:52:50.461131 ad70                       Options.max_log_file_size: 0
2025/05/13-14:52:50.461154 ad70                  Options.max_manifest_file_size: 1073741824
2025/05/13-14:52:50.461167 ad70                   Options.log_file_time_to_roll: 0
2025/05/13-14:52:50.461180 ad70                       Options.keep_log_file_num: 100
2025/05/13-14:52:50.461194 ad70                    Options.recycle_log_file_num: 0
2025/05/13-14:52:50.461208 ad70                         Options.allow_fallocate: 1
2025/05/13-14:52:50.461221 ad70                        Options.allow_mmap_reads: 0
2025/05/13-14:52:50.461236 ad70                       Options.allow_mmap_writes: 0
2025/05/13-14:52:50.461260 ad70                        Options.use_direct_reads: 0
2025/05/13-14:52:50.461268 ad70                        Options.use_direct_io_for_flush_and_compaction: 0
2025/05/13-14:52:50.461275 ad70          Options.create_missing_column_families: 1
2025/05/13-14:52:50.461283 ad70                              Options.db_log_dir: 
2025/05/13-14:52:50.461290 ad70                                 Options.wal_dir: 
2025/05/13-14:52:50.461298 ad70                Options.table_cache_numshardbits: 6
2025/05/13-14:52:50.461304 ad70                         Options.WAL_ttl_seconds: 0
2025/05/13-14:52:50.461313 ad70                       Options.WAL_size_limit_MB: 0
2025/05/13-14:52:50.461330 ad70                        Options.max_write_batch_group_size_bytes: 1048576
2025/05/13-14:52:50.461337 ad70             Options.manifest_preallocation_size: 4194304
2025/05/13-14:52:50.461345 ad70                     Options.is_fd_close_on_exec: 1
2025/05/13-14:52:50.461352 ad70                   Options.advise_random_on_open: 1
2025/05/13-14:52:50.461359 ad70                    Options.db_write_buffer_size: 0
2025/05/13-14:52:50.461366 ad70                    Options.write_buffer_manager: 000002B07F97BC80
2025/05/13-14:52:50.461373 ad70         Options.access_hint_on_compaction_start: 1
2025/05/13-14:52:50.461380 ad70           Options.random_access_max_buffer_size: 1048576
2025/05/13-14:52:50.461387 ad70                      Options.use_adaptive_mutex: 0
2025/05/13-14:52:50.461430 ad70                            Options.rate_limiter: 0000000000000000
2025/05/13-14:52:50.461443 ad70     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/05/13-14:52:50.461451 ad70                       Options.wal_recovery_mode: 2
2025/05/13-14:52:50.461459 ad70                  Options.enable_thread_tracking: 0
2025/05/13-14:52:50.461466 ad70                  Options.enable_pipelined_write: 0
2025/05/13-14:52:50.461473 ad70                  Options.unordered_write: 0
2025/05/13-14:52:50.461490 ad70         Options.allow_concurrent_memtable_write: 1
2025/05/13-14:52:50.461497 ad70      Options.enable_write_thread_adaptive_yield: 1
2025/05/13-14:52:50.461504 ad70             Options.write_thread_max_yield_usec: 100
2025/05/13-14:52:50.461511 ad70            Options.write_thread_slow_yield_usec: 3
2025/05/13-14:52:50.461518 ad70                               Options.row_cache: None
2025/05/13-14:52:50.461526 ad70                              Options.wal_filter: None
2025/05/13-14:52:50.461533 ad70             Options.avoid_flush_during_recovery: 0
2025/05/13-14:52:50.461540 ad70             Options.allow_ingest_behind: 0
2025/05/13-14:52:50.461547 ad70             Options.two_write_queues: 0
2025/05/13-14:52:50.461554 ad70             Options.manual_wal_flush: 0
2025/05/13-14:52:50.461562 ad70             Options.wal_compression: 0
2025/05/13-14:52:50.461569 ad70             Options.atomic_flush: 0
2025/05/13-14:52:50.461577 ad70             Options.avoid_unnecessary_blocking_io: 0
2025/05/13-14:52:50.461589 ad70                 Options.persist_stats_to_disk: 0
2025/05/13-14:52:50.461596 ad70                 Options.write_dbid_to_manifest: 0
2025/05/13-14:52:50.461603 ad70                 Options.log_readahead_size: 0
2025/05/13-14:52:50.461612 ad70                 Options.file_checksum_gen_factory: Unknown
2025/05/13-14:52:50.461626 ad70                 Options.best_efforts_recovery: 0
2025/05/13-14:52:50.461635 ad70                Options.max_bgerror_resume_count: 2147483647
2025/05/13-14:52:50.461643 ad70            Options.bgerror_resume_retry_interval: 1000000
2025/05/13-14:52:50.461650 ad70             Options.allow_data_in_errors: 0
2025/05/13-14:52:50.461657 ad70             Options.db_host_id: __hostname__
2025/05/13-14:52:50.461664 ad70             Options.enforce_single_del_contracts: true
2025/05/13-14:52:50.461671 ad70             Options.max_background_jobs: 2
2025/05/13-14:52:50.461678 ad70             Options.max_background_compactions: 4
2025/05/13-14:52:50.461686 ad70             Options.max_subcompactions: 1
2025/05/13-14:52:50.461694 ad70             Options.avoid_flush_during_shutdown: 0
2025/05/13-14:52:50.461700 ad70           Options.writable_file_max_buffer_size: 1048576
2025/05/13-14:52:50.461707 ad70             Options.delayed_write_rate : 16777216
2025/05/13-14:52:50.461714 ad70             Options.max_total_wal_size: 1073741824
2025/05/13-14:52:50.461721 ad70             Options.delete_obsolete_files_period_micros: 21600000000
2025/05/13-14:52:50.461728 ad70                   Options.stats_dump_period_sec: 600
2025/05/13-14:52:50.461735 ad70                 Options.stats_persist_period_sec: 600
2025/05/13-14:52:50.461751 ad70                 Options.stats_history_buffer_size: 1048576
2025/05/13-14:52:50.461758 ad70                          Options.max_open_files: -1
2025/05/13-14:52:50.461766 ad70                          Options.bytes_per_sync: 0
2025/05/13-14:52:50.461772 ad70                      Options.wal_bytes_per_sync: 0
2025/05/13-14:52:50.461780 ad70                   Options.strict_bytes_per_sync: 0
2025/05/13-14:52:50.461787 ad70       Options.compaction_readahead_size: 0
2025/05/13-14:52:50.461794 ad70                  Options.max_background_flushes: 1
2025/05/13-14:52:50.461801 ad70 Compression algorithms supported:
2025/05/13-14:52:50.461808 ad70 	kZSTD supported: 1
2025/05/13-14:52:50.461816 ad70 	kSnappyCompression supported: 1
2025/05/13-14:52:50.461823 ad70 	kBZip2Compression supported: 0
2025/05/13-14:52:50.461830 ad70 	kZlibCompression supported: 1
2025/05/13-14:52:50.461869 ad70 	kLZ4Compression supported: 1
2025/05/13-14:52:50.461880 ad70 	kXpressCompression supported: 0
2025/05/13-14:52:50.461887 ad70 	kLZ4HCCompression supported: 1
2025/05/13-14:52:50.461894 ad70 	kZSTDNotFinalCompression supported: 1
2025/05/13-14:52:50.461911 ad70 Fast CRC32 supported: Not supported on x86
2025/05/13-14:52:50.461920 ad70 DMutex implementation: std::mutex
2025/05/13-14:52:50.462866 ad70 [db\version_set.cc:5531] Recovering from manifest file: D:\soft\nacos-server-2.2.3\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000019
2025/05/13-14:52:50.463453 ad70 [db\column_family.cc:633] --------------- Options for column family [default]:
2025/05/13-14:52:50.463507 ad70               Options.comparator: leveldb.BytewiseComparator
2025/05/13-14:52:50.463518 ad70           Options.merge_operator: StringAppendOperator
2025/05/13-14:52:50.463529 ad70        Options.compaction_filter: None
2025/05/13-14:52:50.463537 ad70        Options.compaction_filter_factory: None
2025/05/13-14:52:50.463545 ad70  Options.sst_partitioner_factory: None
2025/05/13-14:52:50.463552 ad70         Options.memtable_factory: SkipListFactory
2025/05/13-14:52:50.463561 ad70            Options.table_factory: BlockBasedTable
2025/05/13-14:52:50.463599 ad70            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002B0779EE2B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 000002B07F8532C0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/13-14:52:50.463609 ad70        Options.write_buffer_size: 67108864
2025/05/13-14:52:50.463617 ad70  Options.max_write_buffer_number: 3
2025/05/13-14:52:50.463625 ad70          Options.compression: Snappy
2025/05/13-14:52:50.463653 ad70                  Options.bottommost_compression: Disabled
2025/05/13-14:52:50.463663 ad70       Options.prefix_extractor: rocksdb.FixedPrefix
2025/05/13-14:52:50.463670 ad70   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/13-14:52:50.463678 ad70             Options.num_levels: 7
2025/05/13-14:52:50.463686 ad70        Options.min_write_buffer_number_to_merge: 1
2025/05/13-14:52:50.463693 ad70     Options.max_write_buffer_number_to_maintain: 0
2025/05/13-14:52:50.463700 ad70     Options.max_write_buffer_size_to_maintain: 0
2025/05/13-14:52:50.463708 ad70            Options.bottommost_compression_opts.window_bits: -14
2025/05/13-14:52:50.463715 ad70                  Options.bottommost_compression_opts.level: 32767
2025/05/13-14:52:50.463722 ad70               Options.bottommost_compression_opts.strategy: 0
2025/05/13-14:52:50.463729 ad70         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/13-14:52:50.463736 ad70         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/13-14:52:50.463743 ad70         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/13-14:52:50.463750 ad70                  Options.bottommost_compression_opts.enabled: false
2025/05/13-14:52:50.463757 ad70         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/13-14:52:50.463768 ad70         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/13-14:52:50.463778 ad70            Options.compression_opts.window_bits: -14
2025/05/13-14:52:50.463800 ad70                  Options.compression_opts.level: 32767
2025/05/13-14:52:50.463808 ad70               Options.compression_opts.strategy: 0
2025/05/13-14:52:50.463816 ad70         Options.compression_opts.max_dict_bytes: 0
2025/05/13-14:52:50.463823 ad70         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/13-14:52:50.463831 ad70         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/13-14:52:50.463838 ad70         Options.compression_opts.parallel_threads: 1
2025/05/13-14:52:50.463845 ad70                  Options.compression_opts.enabled: false
2025/05/13-14:52:50.463853 ad70         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/13-14:52:50.463860 ad70      Options.level0_file_num_compaction_trigger: 10
2025/05/13-14:52:50.463868 ad70          Options.level0_slowdown_writes_trigger: 20
2025/05/13-14:52:50.463875 ad70              Options.level0_stop_writes_trigger: 40
2025/05/13-14:52:50.463882 ad70                   Options.target_file_size_base: 67108864
2025/05/13-14:52:50.463889 ad70             Options.target_file_size_multiplier: 1
2025/05/13-14:52:50.463896 ad70                Options.max_bytes_for_level_base: 536870912
2025/05/13-14:52:50.463903 ad70 Options.level_compaction_dynamic_level_bytes: 0
2025/05/13-14:52:50.463910 ad70          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/13-14:52:50.463928 ad70 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/13-14:52:50.463935 ad70 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/13-14:52:50.463943 ad70 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/13-14:52:50.463950 ad70 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/13-14:52:50.463957 ad70 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/13-14:52:50.463965 ad70 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/13-14:52:50.463972 ad70 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/13-14:52:50.463979 ad70       Options.max_sequential_skip_in_iterations: 8
2025/05/13-14:52:50.463987 ad70                    Options.max_compaction_bytes: 1677721600
2025/05/13-14:52:50.463994 ad70                        Options.arena_block_size: 1048576
2025/05/13-14:52:50.464001 ad70   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/13-14:52:50.464008 ad70   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/13-14:52:50.464015 ad70                Options.disable_auto_compactions: 0
2025/05/13-14:52:50.464023 ad70                        Options.compaction_style: kCompactionStyleLevel
2025/05/13-14:52:50.464030 ad70                          Options.compaction_pri: kMinOverlappingRatio
2025/05/13-14:52:50.464038 ad70 Options.compaction_options_universal.size_ratio: 1
2025/05/13-14:52:50.464045 ad70 Options.compaction_options_universal.min_merge_width: 2
2025/05/13-14:52:50.464063 ad70 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/13-14:52:50.464071 ad70 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/13-14:52:50.464078 ad70 Options.compaction_options_universal.compression_size_percent: -1
2025/05/13-14:52:50.464086 ad70 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/13-14:52:50.464093 ad70 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/13-14:52:50.464101 ad70 Options.compaction_options_fifo.allow_compaction: 0
2025/05/13-14:52:50.464111 ad70                   Options.table_properties_collectors: 
2025/05/13-14:52:50.464119 ad70                   Options.inplace_update_support: 0
2025/05/13-14:52:50.464127 ad70                 Options.inplace_update_num_locks: 10000
2025/05/13-14:52:50.464134 ad70               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/05/13-14:52:50.464142 ad70               Options.memtable_whole_key_filtering: 0
2025/05/13-14:52:50.464196 ad70   Options.memtable_huge_page_size: 0
2025/05/13-14:52:50.464208 ad70                           Options.bloom_locality: 0
2025/05/13-14:52:50.464215 ad70                    Options.max_successive_merges: 0
2025/05/13-14:52:50.464222 ad70                Options.optimize_filters_for_hits: 0
2025/05/13-14:52:50.464241 ad70                Options.paranoid_file_checks: 0
2025/05/13-14:52:50.464248 ad70                Options.force_consistency_checks: 1
2025/05/13-14:52:50.464256 ad70                Options.report_bg_io_stats: 0
2025/05/13-14:52:50.464263 ad70                               Options.ttl: 2592000
2025/05/13-14:52:50.464270 ad70          Options.periodic_compaction_seconds: 0
2025/05/13-14:52:50.464278 ad70  Options.preclude_last_level_data_seconds: 0
2025/05/13-14:52:50.464285 ad70                       Options.enable_blob_files: false
2025/05/13-14:52:50.464293 ad70                           Options.min_blob_size: 0
2025/05/13-14:52:50.464300 ad70                          Options.blob_file_size: 268435456
2025/05/13-14:52:50.464307 ad70                   Options.blob_compression_type: NoCompression
2025/05/13-14:52:50.464315 ad70          Options.enable_blob_garbage_collection: false
2025/05/13-14:52:50.464322 ad70      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/13-14:52:50.464330 ad70 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/13-14:52:50.464337 ad70          Options.blob_compaction_readahead_size: 0
2025/05/13-14:52:50.464344 ad70                Options.blob_file_starting_level: 0
2025/05/13-14:52:50.464351 ad70 Options.experimental_mempurge_threshold: 0.000000
2025/05/13-14:52:50.474405 ad70 [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/05/13-14:52:50.474584 ad70               Options.comparator: leveldb.BytewiseComparator
2025/05/13-14:52:50.474618 ad70           Options.merge_operator: StringAppendOperator
2025/05/13-14:52:50.474644 ad70        Options.compaction_filter: None
2025/05/13-14:52:50.474670 ad70        Options.compaction_filter_factory: None
2025/05/13-14:52:50.474695 ad70  Options.sst_partitioner_factory: None
2025/05/13-14:52:50.474721 ad70         Options.memtable_factory: SkipListFactory
2025/05/13-14:52:50.474747 ad70            Options.table_factory: BlockBasedTable
2025/05/13-14:52:50.474840 ad70            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002B0779EE2B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 000002B07F8532C0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/13-14:52:50.474870 ad70        Options.write_buffer_size: 67108864
2025/05/13-14:52:50.474890 ad70  Options.max_write_buffer_number: 3
2025/05/13-14:52:50.474914 ad70          Options.compression: Snappy
2025/05/13-14:52:50.474936 ad70                  Options.bottommost_compression: Disabled
2025/05/13-14:52:50.474964 ad70       Options.prefix_extractor: rocksdb.FixedPrefix
2025/05/13-14:52:50.474988 ad70   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/13-14:52:50.475018 ad70             Options.num_levels: 7
2025/05/13-14:52:50.475041 ad70        Options.min_write_buffer_number_to_merge: 1
2025/05/13-14:52:50.475096 ad70     Options.max_write_buffer_number_to_maintain: 0
2025/05/13-14:52:50.475117 ad70     Options.max_write_buffer_size_to_maintain: 0
2025/05/13-14:52:50.475137 ad70            Options.bottommost_compression_opts.window_bits: -14
2025/05/13-14:52:50.475156 ad70                  Options.bottommost_compression_opts.level: 32767
2025/05/13-14:52:50.475181 ad70               Options.bottommost_compression_opts.strategy: 0
2025/05/13-14:52:50.475191 ad70         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/13-14:52:50.475200 ad70         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/13-14:52:50.475208 ad70         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/13-14:52:50.475216 ad70                  Options.bottommost_compression_opts.enabled: false
2025/05/13-14:52:50.475224 ad70         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/13-14:52:50.475234 ad70         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/13-14:52:50.475243 ad70            Options.compression_opts.window_bits: -14
2025/05/13-14:52:50.475251 ad70                  Options.compression_opts.level: 32767
2025/05/13-14:52:50.475258 ad70               Options.compression_opts.strategy: 0
2025/05/13-14:52:50.475266 ad70         Options.compression_opts.max_dict_bytes: 0
2025/05/13-14:52:50.475274 ad70         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/13-14:52:50.475284 ad70         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/13-14:52:50.475305 ad70         Options.compression_opts.parallel_threads: 1
2025/05/13-14:52:50.475316 ad70                  Options.compression_opts.enabled: false
2025/05/13-14:52:50.475325 ad70         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/13-14:52:50.475333 ad70      Options.level0_file_num_compaction_trigger: 10
2025/05/13-14:52:50.475340 ad70          Options.level0_slowdown_writes_trigger: 20
2025/05/13-14:52:50.475349 ad70              Options.level0_stop_writes_trigger: 40
2025/05/13-14:52:50.475358 ad70                   Options.target_file_size_base: 67108864
2025/05/13-14:52:50.475367 ad70             Options.target_file_size_multiplier: 1
2025/05/13-14:52:50.475377 ad70                Options.max_bytes_for_level_base: 536870912
2025/05/13-14:52:50.475385 ad70 Options.level_compaction_dynamic_level_bytes: 0
2025/05/13-14:52:50.475394 ad70          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/13-14:52:50.475404 ad70 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/13-14:52:50.475411 ad70 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/13-14:52:50.475419 ad70 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/13-14:52:50.475426 ad70 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/13-14:52:50.475434 ad70 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/13-14:52:50.475459 ad70 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/13-14:52:50.475467 ad70 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/13-14:52:50.475475 ad70       Options.max_sequential_skip_in_iterations: 8
2025/05/13-14:52:50.475483 ad70                    Options.max_compaction_bytes: 1677721600
2025/05/13-14:52:50.475490 ad70                        Options.arena_block_size: 1048576
2025/05/13-14:52:50.475498 ad70   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/13-14:52:50.475506 ad70   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/13-14:52:50.475514 ad70                Options.disable_auto_compactions: 0
2025/05/13-14:52:50.475524 ad70                        Options.compaction_style: kCompactionStyleLevel
2025/05/13-14:52:50.475533 ad70                          Options.compaction_pri: kMinOverlappingRatio
2025/05/13-14:52:50.475541 ad70 Options.compaction_options_universal.size_ratio: 1
2025/05/13-14:52:50.475553 ad70 Options.compaction_options_universal.min_merge_width: 2
2025/05/13-14:52:50.475562 ad70 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/13-14:52:50.475570 ad70 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/13-14:52:50.475577 ad70 Options.compaction_options_universal.compression_size_percent: -1
2025/05/13-14:52:50.475586 ad70 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/13-14:52:50.475594 ad70 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/13-14:52:50.475613 ad70 Options.compaction_options_fifo.allow_compaction: 0
2025/05/13-14:52:50.475629 ad70                   Options.table_properties_collectors: 
2025/05/13-14:52:50.475638 ad70                   Options.inplace_update_support: 0
2025/05/13-14:52:50.475646 ad70                 Options.inplace_update_num_locks: 10000
2025/05/13-14:52:50.475654 ad70               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/05/13-14:52:50.475663 ad70               Options.memtable_whole_key_filtering: 0
2025/05/13-14:52:50.475671 ad70   Options.memtable_huge_page_size: 0
2025/05/13-14:52:50.475681 ad70                           Options.bloom_locality: 0
2025/05/13-14:52:50.475690 ad70                    Options.max_successive_merges: 0
2025/05/13-14:52:50.475698 ad70                Options.optimize_filters_for_hits: 0
2025/05/13-14:52:50.475705 ad70                Options.paranoid_file_checks: 0
2025/05/13-14:52:50.475715 ad70                Options.force_consistency_checks: 1
2025/05/13-14:52:50.475723 ad70                Options.report_bg_io_stats: 0
2025/05/13-14:52:50.475731 ad70                               Options.ttl: 2592000
2025/05/13-14:52:50.475738 ad70          Options.periodic_compaction_seconds: 0
2025/05/13-14:52:50.475745 ad70  Options.preclude_last_level_data_seconds: 0
2025/05/13-14:52:50.475765 ad70                       Options.enable_blob_files: false
2025/05/13-14:52:50.475772 ad70                           Options.min_blob_size: 0
2025/05/13-14:52:50.475781 ad70                          Options.blob_file_size: 268435456
2025/05/13-14:52:50.475788 ad70                   Options.blob_compression_type: NoCompression
2025/05/13-14:52:50.475796 ad70          Options.enable_blob_garbage_collection: false
2025/05/13-14:52:50.475805 ad70      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/13-14:52:50.475813 ad70 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/13-14:52:50.475821 ad70          Options.blob_compaction_readahead_size: 0
2025/05/13-14:52:50.475831 ad70                Options.blob_file_starting_level: 0
2025/05/13-14:52:50.475839 ad70 Options.experimental_mempurge_threshold: 0.000000
2025/05/13-14:52:50.523114 ad70 [db\version_set.cc:5579] Recovered from manifest file:D:\soft\nacos-server-2.2.3\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000019 succeeded,manifest_file_number is 19, next_file_number is 21, last_sequence is 5, log_number is 13,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 13
2025/05/13-14:52:50.523208 ad70 [db\version_set.cc:5588] Column family [default] (ID 0), log number is 13
2025/05/13-14:52:50.523235 ad70 [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 13
2025/05/13-14:52:50.523803 ad70 [db\db_impl\db_impl_open.cc:529] DB ID: b669815c-2a47-11f0-9a76-f0def1935d90
2025/05/13-14:52:50.525013 ad70 EVENT_LOG_v1 {"time_micros": 1747119170525005, "job": 1, "event": "recovery_started", "wal_files": [18]}
2025/05/13-14:52:50.525044 ad70 [db\db_impl\db_impl_open.cc:1029] Recovering log #18 mode 2
2025/05/13-14:52:50.561640 ad70 EVENT_LOG_v1 {"time_micros": 1747119170561612, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 22, "file_size": 1073, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 6, "largest_seqno": 11, "table_properties": {"data_size": 0, "index_size": 13, "index_partitions": 0, "top_level_index_size": 8, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 16, "raw_average_value_size": 8, "num_data_blocks": 0, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 2, "num_merge_operands": 0, "num_range_deletions": 2, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1747119170, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "b669815c-2a47-11f0-9a76-f0def1935d90", "db_session_id": "YQ48OGY1SY7MI8000H6D", "orig_file_number": 22, "seqno_to_time_mapping": "N/A"}}
2025/05/13-14:52:50.585193 ad70 EVENT_LOG_v1 {"time_micros": 1747119170585160, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 23, "file_size": 1219, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 7, "largest_seqno": 12, "table_properties": {"data_size": 44, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 58, "raw_average_key_size": 19, "raw_value_size": 24, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 3, "num_filter_entries": 0, "num_deletions": 2, "num_merge_operands": 0, "num_range_deletions": 2, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1747119170, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "b669815c-2a47-11f0-9a76-f0def1935d90", "db_session_id": "YQ48OGY1SY7MI8000H6D", "orig_file_number": 23, "seqno_to_time_mapping": "N/A"}}
2025/05/13-14:52:50.592895 ad70 EVENT_LOG_v1 {"time_micros": 1747119170592885, "job": 1, "event": "recovery_finished"}
2025/05/13-14:52:50.593485 ad70 [db\version_set.cc:5051] Creating manifest 25
2025/05/13-14:52:50.689461 ad70 [file\delete_scheduler.cc:77] Deleted file D:\soft\nacos-server-2.2.3\nacos\data\protocol\raft\naming_persistent_service_v2\log/000018.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/05/13-14:52:50.689494 ad70 [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 000002B00238E410
2025/05/13-14:52:50.689901 ad70 DB pointer 000002B07E029500
2025/05/13-14:52:53.697935 7964 [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/05/13-14:52:53.697998 7964 [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 3.2 total, 3.2 interval
Cumulative writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5 writes, 5 keys, 5 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 5 writes, 1 syncs, 5.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.23 KB   0.3      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.030       0      0       0.0       0.0
 Sum      3/0    3.23 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.030       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.030       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.03              0.00         1    0.030       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.2 total, 3.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@000002B07F8532C0#67532 capacity: 512.00 MB usage: 2.51 KB table_size: 4096 occupancy: 13 collections: 1 last_copies: 1 last_secs: 0.000643 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(5,0.67 KB,0.000127777%) IndexBlock(5,0.47 KB,8.9407e-05%) OtherBlock(2,0.27 KB,5.1409e-05%) Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.44 KB   0.3      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.021       0      0       0.0       0.0
 Sum      3/0    3.44 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.021       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.021       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.02              0.00         1    0.021       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.2 total, 3.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@000002B07F8532C0#67532 capacity: 512.00 MB usage: 2.51 KB table_size: 4096 occupancy: 13 collections: 1 last_copies: 1 last_secs: 0.000643 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(5,0.67 KB,0.000127777%) IndexBlock(5,0.47 KB,8.9407e-05%) OtherBlock(2,0.27 KB,5.1409e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 7 Average: 3432.7143  StdDev: 5533.80
Min: 3  Median: 18.5000  Max: 14174
Percentiles: P50: 18.50 P75: 7425.00 P99: 14174.00 P99.9: 14174.00 P99.99: 14174.00
------------------------------------------------------
(       2,       3 ]        1  14.286%  14.286% ###
(       4,       6 ]        1  14.286%  28.571% ###
(      15,      22 ]        3  42.857%  71.429% #########
(    6600,    9900 ]        1  14.286%  85.714% ###
(   14000,   22000 ]        1  14.286% 100.000% ###


** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 9 Average: 837.7778  StdDev: 1798.03
Min: 2  Median: 12.5000  Max: 5678
Percentiles: P50: 12.50 P75: 32.50 P99: 5678.00 P99.9: 5678.00 P99.99: 5678.00
------------------------------------------------------
(       1,       2 ]        1  11.111%  11.111% ##
(       2,       3 ]        1  11.111%  22.222% ##
(       4,       6 ]        1  11.111%  33.333% ##
(       6,      10 ]        1  11.111%  44.444% ##
(      10,      15 ]        1  11.111%  55.556% ##
(      22,      34 ]        2  22.222%  77.778% ####
(    1300,    1900 ]        1  11.111%  88.889% ##
(    4400,    6600 ]        1  11.111% 100.000% ##

2025/05/13-14:52:53.699176 7964 [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 12
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 12
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 5
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 5
rocksdb.block.cache.index.bytes.insert COUNT : 480
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 7
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 7
rocksdb.block.cache.data.bytes.insert COUNT : 962
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 1442
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 5
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 168
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 1
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 26
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 168
rocksdb.write.self COUNT : 5
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 5
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2292
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 2
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 1
rocksdb.number.block.decompressed COUNT : 1
rocksdb.number.block.not_compressed COUNT : 3
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 1
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 12932
rocksdb.non.last.level.read.count COUNT : 16
rocksdb.block.checksum.compute.count COUNT : 24
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 69.750000 P95 : 42707.000000 P99 : 42707.000000 P100 : 42707.000000 COUNT : 5 SUM : 43060
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 22000.000000 P95 : 29015.000000 P99 : 29015.000000 P100 : 29015.000000 COUNT : 2 SUM : 49066
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 42649.000000 P95 : 42649.000000 P99 : 42649.000000 P100 : 42649.000000 COUNT : 1 SUM : 42649
rocksdb.manifest.file.sync.micros P50 : 28042.000000 P95 : 28042.000000 P99 : 28042.000000 P100 : 28042.000000 COUNT : 1 SUM : 28042
rocksdb.table.open.io.micros P50 : 1900.000000 P95 : 14293.000000 P99 : 14293.000000 P100 : 14293.000000 COUNT : 6 SUM : 32207
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 1.000000 P95 : 10.500000 P99 : 12.000000 P100 : 12.000000 COUNT : 18 SUM : 47
rocksdb.write.raw.block.micros P50 : 0.714286 P95 : 2.750000 P99 : 2.950000 P100 : 3.000000 COUNT : 10 SUM : 13
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 17.333333 P95 : 14174.000000 P99 : 14174.000000 P100 : 14174.000000 COUNT : 16 SUM : 31569
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 31.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 5 SUM : 168
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 45.000000 P95 : 45.000000 P99 : 45.000000 P100 : 45.000000 COUNT : 1 SUM : 45
rocksdb.bytes.decompressed P50 : 45.000000 P95 : 45.000000 P99 : 45.000000 P100 : 45.000000 COUNT : 1 SUM : 45
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1085.000000 P95 : 1140.000000 P99 : 1140.000000 P100 : 1140.000000 COUNT : 5 SUM : 5267
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/05/13-14:52:54.704016 7964 [db\db_impl\db_impl.cc:927] ------- PERSISTING STATS -------
2025/05/13-14:52:54.704057 7964 [db\db_impl\db_impl.cc:997] [Pre-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/05/13-14:52:54.704064 7964 [db\db_impl\db_impl.cc:1006] [Post-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/05/13-14:57:59.528627 2364 [db\db_impl\db_impl.cc:497] Shutdown: canceling all background work
2025/05/13-14:57:59.648192 2364 [db\db_impl\db_impl.cc:704] Shutdown complete
